<template>
  <view>
    <scroll-view class="main" scroll-y :scroll-with-animation="true">
      <view class="container">
        <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
          <!-- 基本信息 -->
          <u-form-item label="合同编号" required prop="saleContractCode">
            <u-input v-model="form.saleContractCode" placeholder="请输入合同编号" :custom-style="customStyle"
              :placeholder-style="placeholderStyle" />
          </u-form-item>

          <!-- 公司信息 -->
          <u-form-item label="需求方公司名称" required prop="nmbCompanyName" :right-icon="'arrow-right'">
            <u-input disabled @click="showCompany = true" v-model="form.nmbCompanyName" placeholder="请选择需求方公司名称"
              :custom-style="customStyle" :placeholder-style="placeholderStyle" />
          </u-form-item>

          <!-- 地址信息 -->
          <u-form-item label="需求方所在地区" required prop="province" :right-icon="'arrow-right'"  style="text-align: right">
            <text :class="form.province ? 'common' : 'tips'" @click="pickerAreaShow = true">
              {{ form.province ? `${form.province}-${form.city}` : '请选择需求方所在区' }}
            </text>
          </u-form-item>

          <u-form-item label="需求方详细地址" prop="detailAddress">
            <u-input v-model="form.detailAddress" placeholder="请输入需求方详细地址" :custom-style="customStyle"
              :placeholder-style="placeholderStyle" />
          </u-form-item>

          <!-- 人员信息 -->
          <u-form-item label="销售员" required prop="userName">
            <u-input v-model="userName" disabled placeholder="请输入销售员名称" :custom-style="customStyle"
              :placeholder-style="placeholderStyle" />
          </u-form-item>

          <u-form-item label="项目经理" required prop="projectManagerName" :right-icon="'arrow-right'">
            <u-input disabled @click="showProject = true" v-model="projectManagerName" placeholder="请选择项目经理"
              :custom-style="customStyle" :placeholder-style="placeholderStyle" />
          </u-form-item>

          <!-- 合同时间 -->
          <u-form-item label="合同开始日期" prop="contractStartTime" required style="text-align: right" :right-icon="'arrow-right'">
            <text :class="form.contractStartTime ? 'common' : 'tips'" @click="showDataPicker(0)">
              {{ form.contractStartTime || "请选择合同开始日期" }}
            </text>
          </u-form-item>

          <u-form-item label="合同结束日期" prop="contractEndTime" required style="text-align: right" :right-icon="'arrow-right'">
            <text :class="form.contractEndTime ? 'common' : 'tips'" @click="showDataPicker(1)">
              {{ form.contractEndTime || "请选择合同结束日期" }}
            </text>
          </u-form-item>

          <!-- 合同文件 -->
          <u-form-item label="合同文件（pdf不超过一个）" prop="contractUrl" required :custom-style="customStyle"
            :border-bottom="!form.contractUrl.length">
            <u-input placeholder=" " disabled />
            <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadPdf('contractUrl', 1)"
              src="../../../myPackge2/icon/pdf.png" alt="" />
          </u-form-item>

          <!-- 已上传文件列表 -->
          <view class="uploadImage" v-if="form.contractUrl.length">
            <view class="itemAlready pdf-box" v-for="(item, index) in form.contractUrl" :key="index"
              @click="previewFiles(item)">
              {{ contractUrlName }}
              <view class="closeIcon close-file" @click.stop="deleteFile(index)"></view>
            </view>
          </view>
        </u-form>
      </view>
      <div style="height:5px"></div>
    </scroll-view>

    <!-- 底部提交按钮 -->
    <view class="bg-box">
      <view class="add-btn" @click="addPayment">提交</view>
    </view>

    <!-- 选择器组件 -->
    <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow" @addressCanel="pickerAreaShow = false"
      :titleShow="false" />

    <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`date`" @change='changeData'  :max-date='maxdata' :min-date="mindata"></u-calendar>

   <!-- <term-picker :timeLimit="false" :deferYear="100" :minYear="2023" @changeTime="changeStartTime"
    :visable.sync="startVisable" />

    <term-picker :timeLimit="false" :deferYear="100" :minYear="2023" @changeTime="changeEndTime"
    :visable.sync="endVisable" /> -->

    <u-select confirm-color='#40CA8F' v-model="showCompany" mode="single-column" :list="companyCustomerList" label-name="companyName"
      @confirm="selectPastureType" value-name="nmbCompanyId" />

    <u-select confirm-color='#40CA8F' v-model="showProject" mode="single-column" :list="projectList" label-name="nickName"
      @confirm="selectProject" value-name="nmbUserId" />
  </view>
</template>

<script>
import { uploadFiles } from '@/api/obsUpload/index'
import addressPicker from '@/components/address-picker/index.vue'
import { saleContractAdd, companyCustomer, tradingPage } from '@/api/pages/salesContract'
import termPicker from "@/components/term-picker/term-picker"

export default {
  name: 'CreateContract',
  
  components: { 
    addressPicker, 
    termPicker 
  },

  data() {
    return {
		mindata: "1995-09-09",
		maxdata:'2095-09-09',
		showData:false,
		dateType:0,
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        saleContractCode: '',
        nmbCompanyId: '',
        province: '',
        detailAddress: '',
        projectManagerId: '',
        contractStartTime: '',
        contractEndTime: '',
        contractUrl: [],
      },
      userName: '',
      projectManagerName: '',
      companyCustomerList: [],
      projectList: [],
      showCompany: false,
      showProject: false,
      pickerAreaShow: false,
      errorType: ['message'],
      startVisable: false,
      endVisable: false,
      customStyle: { textAlign: 'right', fontSize: '26rpx' },
      labelStyle: { color: '#333', fontSize: '26rpx' },
      placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
      fileType: ['pdf'],
      contractUrlName: '',
      isSubmitting: false,
      rules: {
        saleContractCode: [{
          required: true,
          message: '请输入合同编号',
          trigger: ['blur', 'change']
        }],
        nmbCompanyName: [{
          required: true,
          message: '请选择需求方公司名称',
          trigger: ['blur', 'change']
        }],
        province: [{
          required: true,
          message: '请选择需求方所在区',
          trigger: ['blur', 'change']
        }],
        contractStartTime: [{
          required: true,
          message: '请选择合同开始日期',
          trigger: ['blur', 'change']
        }],
        contractEndTime: [{
          required: true,
          message: '请选择合同结束日期',
          trigger: ['blur', 'change']
        }],
        /* contractUrl: [{
          required: true,
          message: '请上传合同文件',
          trigger: ['blur', 'change']
        }] */
        contractUrl: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传合同文件'))},
          message: '请上传合同文件',
          trigger: 'change'
        }],
      }
    }
  },

  computed: {
    isFormValid() {
      const { 
        saleContractCode, 
        nmbCompanyId, 
        province,
        contractStartTime,
        contractEndTime,
        contractUrl 
      } = this.form
      
      return saleContractCode && 
        nmbCompanyId && 
        province && 
        contractStartTime && 
        contractEndTime && 
        contractUrl.length > 0
    }
  },

  onLoad() {
    try {
		
		// 创建一个 Date 对象以获取当前日期和时间
		const currentDate = new Date();
		
		// 获取当前年份和月份
		const currentYear = currentDate.getFullYear();
		const currentMonth = currentDate.getMonth();
		
		// 计算前一个月的年份和月份
		let previousYear = currentYear;
		let previousMonth = currentMonth - 1;
		
		// 如果前一个月是上一年的 12 月，则更新年份和月份
		if (previousMonth < 0) {
		    previousYear--;
		    previousMonth = 11;
		}
		
		// 创建一个新的 Date 对象来表示前一个月的日期
		const previousMonthDate = new Date(previousYear, previousMonth, currentDate.getDate());		
		this.mindata = previousMonthDate.toLocaleDateString().replace(/\//g, '-');
     
		
      const userInfo = uni.getStorageSync('userInfo')
      if (!userInfo?.sysUser?.corprateName) {
        throw new Error('未获取到用户信息')
      }
      this.userName = userInfo.sysUser.nickName || userInfo.sysUser.corprateName
      this.initData()
    } catch (error) {
      this.handleError(error)
    }
  },

  onReady() {
    this.$refs.uForm?.setRules?.(this.rules)
  },

  methods: {
	  
	  changeData(e){
		if(this.dateType == 0) this.form.contractStartTime = e.result;
		else this.form.contractEndTime = e.result;
		this.showData = false;
	  },
	  
	 showDataPicker(type){
		
		 this.dateType = type;
		 
		 if(this.dateType == 1 && this.form.contractStartTime == '') return this.$toast("请选选择合同起始日期")
		 
		 
		 this.mindata = this.dateType == 0 ? this.mindata : this.form.contractStartTime;
		 
	
		 
		 this.showData = true;
		 
	 },
	  
   
    // 初始化数据
    async initData() {
      try {
        await this.getCompanyCustomerList()
      } catch (error) {
        this.handleError(error)
      }
    },

    // 文件处理相关方法
    async uploadPdf(type, maxCount) {
      try {
        if (!this.validateUploadCount(type, maxCount)) return
        
        const file = await this.selectAndValidateFile()
        if (!file) return
        
        await this.uploadAndSaveFile(file, type)
      } catch (error) {
        this.handleError(error, '上传失败')
      }
    },

    validateUploadCount(type, maxCount) {
      if (this.form[type].length >= maxCount) {
        this.$toast(`pdf最多只能上传${maxCount}个`)
        return false
      }
      return true
    },

    async selectAndValidateFile() {
      const { tempFiles } = await this.chooseFile()
      const file = tempFiles[0]
      return this.validateFile(file) ? file : null
    },

    async uploadAndSaveFile(file, type) {
      const uploadedUrl = await uploadFiles({
        filePath: file.path,
        name: file.name,
      })
      
      if (!uploadedUrl) throw new Error('上传失败')
      
      this.form[type].push(uploadedUrl)
      this.contractUrlName = file.name
    },

    validateFile(file) {
      if (!file) return false
      
      if (file.type !== 'file') {
        this.$toast('请上传pdf文件')
        return false
      }

      const extension = file.name.split('.').pop()?.toLowerCase()
      if (!extension || !this.fileType.includes(extension)) {
        this.$toast('请上传pdf文件')
        return false
      }

      return true
    },

    chooseFile() {
      return new Promise((resolve, reject) => {
        uni.chooseMessageFile({
          type: 'file',
          count: 1,
          success: resolve,
          fail: (error) => {
            reject(new Error('选择文件失败: ' + error.errMsg))
          }
        })
      })
    },

    // 时间处理相关方法
    changeStartTime(value) {
      if (!value) return
      this.form.contractStartTime = value
      this.startVisable = false
      this.resetField('contractStartTime')
      
      // 验证开始时间不能大于结束时间
      if (this.form.contractEndTime) {
        if (new Date(value) > new Date(this.form.contractEndTime)) {
          this.$toast('开始时间不能大于结束时间')
          this.form.contractStartTime = ''
        }
      }
    },

    changeEndTime(value) {
      if (!value) return
      this.form.contractEndTime = value
      this.endVisable = false
      this.resetField('contractEndTime')
      
      // 验证结束时间不能小于开始时间
      if (this.form.contractStartTime) {
        if (new Date(value) < new Date(this.form.contractStartTime)) {
          this.$toast('结束时间不能小于开始时间')
          this.form.contractEndTime = ''
        }
      }
    },

    // 地址选择相关方法
    submitAddress(val) {
      if (!val?.areaName || !val?.areaValue) return
      
      const areaName = val.areaName.split('-')
      const areaId = val.areaValue.split(',')
      
      this.form = {
        ...this.form,
        province: areaName[0] || '',
        provinceId: areaId[0] || '',
        city: areaName[1] || '',
        cityId: areaId[1] || ''
      }
      
      this.pickerAreaShow = false
      this.resetField('province')
    },

    // API 调用相关方法
    async getCompanyCustomerList() {
      const params = { roleKey: 'company_nmb_project' }
      
      const [companyRes, projectRes] = await Promise.all([
        companyCustomer(params),
        tradingPage(params)
      ])
      
      if (!companyRes?.result?.list || !projectRes?.result?.list) {
        throw new Error('获取数据失败')
      }

      this.companyCustomerList = companyRes.result.list
      this.projectList = projectRes.result.list
    },

    async addPayment() {
      if (this.isSubmitting) return
      if (!this.isFormValid) {
        return this.$toast('请填写完整信息')
      }

      try {
        this.isSubmitting = true
        const valid = await this.validateForm()
        if (!valid) return

        const params = {
          ...this.form,
          contractUrl: this.form.contractUrl.join(',')
        }
        
        const res = await saleContractAdd(params)
        if (res.code === 200) {
          uni.$emit('updateSalesContractList')
          this.$toast('添加成功')
          uni.navigateBack({ delta: 1 })
        } else {
          throw new Error(res.message || '提交失败')
        }
      } catch (error) {
        this.handleError(error, '提交失败1')
      } finally {
        this.isSubmitting = false
      }
    },

    validateForm() {
      return new Promise(resolve => {
        this.$refs.uForm.validate(valid => resolve(valid))
      })
    },

    handleError(error, customMessage = '') {
      console.error(error)
      this.$toast(error.message || customMessage || '操作失败')
    },

    resetField(value) {
      if (!value) return
      this.$refs.uForm?.fields?.forEach(field => {
        if (field.prop === value) {
          field.resetField()
        }
      })
    },

    async previewFiles(url) {
      if (!url) return
      
      try {
        const res = await new Promise((resolve, reject) => {
          wx.downloadFile({
            url,
            success: resolve,
            fail: reject
          })
        })

        await new Promise((resolve, reject) => {
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            showMenu: true,
            success: resolve,
            fail: reject
          })
        })
      } catch (error) {
        this.handleError(error, '预览失败')
      }
    },

    deleteFile(index) {
      if (typeof index !== 'number') return
      this.form.contractUrl.splice(index, 1)
      this.$forceUpdate()
    },

    selectPastureType(value) {
      if (!value?.length) return
      
      const companyInfo = this.companyCustomerList.find(item => item.nmbCompanyId === value[0].value)
      if (!companyInfo) return
      
      this.form = {
        ...this.form,
        nmbCompanyId: companyInfo.nmbCompanyId,
        nmbCompanyName: companyInfo.companyName,
        province: companyInfo.provinceName,
        city: companyInfo.cityName,
        provinceId: companyInfo.provinceId,
        cityId: companyInfo.cityId,
        detailAddress: companyInfo.detailAddress
      }
      
      this.showCompany = false
    },

    selectProject(value) {
      if (!value?.length) return
      
      this.form.projectManagerId = value[0].value
      this.projectManagerName = value[0].label
      this.showProject = false
    },
  },
}
</script>

<style lang="less" scoped>
.container {
  margin: 25rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 30rpx 32rpx 40rpx;
  border-radius: 30rpx;

  /deep/ .u-form-item {
    padding: 20rpx 20rpx !important;
  }

  .tips {
    font-size: 28rpx;
    color: #999;
  }

  .voucher {
    padding-bottom: 40rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
  }

  .all-img {
    image {
      width: 154rpx;
      height: 154rpx;
      background: #d8d8d8;
      border-radius: 16rpx;
      margin-right: 20rpx;
    }
  }

  .manyMode {
    background: transparent;
    height: auto;
  }

  .uploadImage {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    // align-content: space-between;
    position: relative;

    .itemAlready {
      // width: 100%;
      // height: 140rpx;
      border-radius: 8rpx;
      margin: 0 20rpx 10rpx 0rpx;
      display: flex;
      flex-wrap: wrap;

      .image-box {
        position: relative;
      }

      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .closeIcon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('../../../static/modalImg/error.png');
        position: absolute;
        background-size: cover;
        top: -10rpx;
        right: 10rpx;
      }
    }

    .item {
      width: 140rpx;
      height: 140rpx;
      border-radius: 8rpx;
      position: relative;
      border: 2rpx dashed #d8d8d8;

      .uploadIcon {
        width: 100%;
        height: 120rpx;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #d8d8d8;
        // background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
        background-size: 20rpx 20rpx;
        background-position: center 30rpx;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        z-index: 5;
      }
    }
  }
}

.bg-box {
  height: 150rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-btn {
  position: fixed;
  z-index: 9;
  height: 80rpx;
  width: 690rpx;
  height: 86rpx;
  background: linear-gradient( 101deg, #19AF77 0%, #40CA8F 100%);
  border-radius: 50rpx;
  font-weight: 600;
  font-size: 34rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 86rpx;
}

.tips {
  font-size: 28rpx;
  color: #c0c3ca;
}

.common {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  color: #333;
  margin: 10rpx;
  text-align: right;
}

.pdf-box {
  width: auto;
  height: auto;
  color: #37BA7E;
  margin: 20rpx 20rpx 10rpx 0rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
  position: relative;
}

.close-file {
  top: 6rpx !important;
  right: -20rpx !important;
}
</style>
