<template>
  <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
    <view class="header">
      <view class="fifter">
        <img :src="`${obs}/nmb-mini/xiaoshouhetong/shaixuan.png`" alt="" @click="fifterClick" />
      </view>
    </view>

    <scroll-view class="main" scroll-y :scroll-with-animation="true" @scrolltolower="scrollToLower" refresher-enabled
      :refresher-triggered="refresherState" @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
      <contractList v-if="!isEmpty"  :list="list" />
      <nullList v-if="isEmpty" />
      <view v-if="!isEmpty" :style="'height:' + (isIphonex ? 48 : 24) + 'rpx'"></view>
    </scroll-view>

    <view class="Add" @click="addIntent" v-if="$hasPermi('nmb:saleContract:add')">
      <img :src="`${obs}/nmb-mini/xiaoshouhetong/add.png`" alt="" />
    </view>
    <filterPopup @resetSearch="resetSearch" :pickerFilterShow="pickerFilterShow" @canel="pickerFilterShow = false"
      @submitForm="submitForm" />
  </view>
</template>

<script>
import filterPopup from '@/components/filterPopup/index.vue'
import contractList from './component/contractList.vue'
import nullList from '@/components/null-list/index.vue'
import { mapState } from 'vuex'
import { getStorage } from '@/common/utils/storage.js'
import { saleContractPage } from '@/api/pages/salesContract'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
const app = getApp();
export default {
    components: {
        CustomNavbar, filterPopup, contractList, nullList },
  data() {
    return {
	  obs : app.globalData.obs,
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      isEmpty: false,
      pickerFilterShow: false,
      filters: {},
      list: [],
      noMore: false,
      pageSize: 10,
      pageNum: 1,
      refresherState: false,
      scrollTop: 0,
    }
  },
  onLoad() {
    this.getList()
    uni.$on('updateSalesContractList', () => {
      this.getList();
      console.log('updateSalesContractList')
    })
    console.log('getList')
  },
  onUnload() {
    uni.$off('updateSalesContractList');
  },
  onShow() { },
  computed: {
    ...mapState({
      userInfo: (state) => state.userDetail.user,
    }),
  },
  methods: {
    getList(val) {
      uni.showLoading({
        title: '加载中',
        icon: 'none',
      })
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        ...val
      }
      saleContractPage(params).then(res => {
        let pendingApproval = res.result?.list || [];
        let total = res.result.total || 0
        if (this.pageNum >= 2) {
          this.list = this.list.concat(pendingApproval)
          this.list.length >= total ? this.noMore = true : this.noMore = false;
        } else {
          if (total >= 1) {
            this.isEmpty = false;
            this.list = pendingApproval;
            this.list.length >= total ? this.noMore = true : this.noMore = false;
          } else {
            this.isEmpty = true;
          }
        }
      })
      uni.hideLoading()
    },
    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList();
    },
    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      setTimeout(() => {
        this.refresherState = false
        this.getList()
        this.$toast('刷新成功')
      }, 1000)
    },

    // 搜索
    fifterClick() {
      this.pickerFilterShow = true
    },

    addIntent() {
      uni.navigateTo({
        url: `/myPackge1/pages/salesContract/createContract`,
      })
    },
    resetSearch() {
      this.getList()
    },
    submitForm(val) {
      console.log(val)
      this.pickerFilterShow = false
      this.getList(val)
    },
  },
}
</script>

<style lang="scss" scoped>
.header{
  width: 750rpx;
  height: 727rpx;
	display: flex;
	padding-top: 120rpx;
	/* 导航栏空间 */
	box-sizing: border-box;
	position: relative;
	background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/hetong.png) no-repeat 100% 100%;
	background-size: 100% 100%;
  position: relative;
}
.fifter{
  position: absolute;
  top: 195rpx;
  right: 30rpx;
  img{
    width: 34rpx;
    height: 32.5rpx;
  }
}
.main{
  margin-top: -372rpx;
}
.Add{
  width: 152rpx;
  height: 145rpx;
  position: absolute;
  bottom: 290rpx;
  right:10rpx;
  img{
    width: 152rpx;
    height: 145rpx;
  }
}
</style>
