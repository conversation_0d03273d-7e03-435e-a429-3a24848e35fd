<template>
    <view>
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <!-- 头部背景区域 -->
        <view class="header">
            <view class="searchs_section">
                <view class="search_box">
                    <view class="search">
                        <u-search v-model="searchText" @search="handleSearch" placeholder="请输入牛源地、牛经纪" bgColor="#FFFFFF"
                            placeholderColor="#A5B2AC" :use-action-icon="true" searchIconSize="30rpx"
                            searchIcon="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/sousuo.png"
                            :show-action="false">
                        </u-search>
                    </view>
                    <view class="fifter">
                        <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                            alt="" @click="fifterClick" />
                    </view>
                </view>
            </view>
        </view>

        <!-- 统计卡片区域 -->
        <!-- <view class="stats-section">
            <view class="stats-card">
                <view class="stats-icon">
                    <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/num.png" alt="">
                </view>
                <view class="stats-label">采购计划个数</view>
            </view>
            <view class="stats-card">
                <view class="stats-icon">
                    <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/jihua.png" alt="">
                </view>
                <view class="stats-label">计划采购合计</view>
            </view>
            <view class="stats-card">
                <view class="stats-icon">
                    <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/heji.png" alt="">
                </view>
                <view class="stats-label">实际采购合计</view>
            </view>
        </view> -->
        <view class="stats-container">
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/num.png"
                        mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.purchaseOrderNumber > 0">{{
                        paramsData.purchaseOrderNumber || 0 }}</view>
                </view>
                <view class="stats-label">采购计划个数</view>
            </view>
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/jihua.png"
                        mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.purchaseLivestockNumber > 0">{{
                        paramsData.purchaseLivestockNumber || 0 }}头</view>
                </view>
                <view class="stats-label">计划采购合计</view>
            </view>
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/heji.png"
                        mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.purchaseNumber > 0">{{ paramsData.purchaseNumber || 0 }}头
                    </view>
                </view>
                <view class="stats-label">实际采购合计</view>
            </view>
        </view>
        <scroll-view class="main" scroll-y :scroll-with-animation="true" @scrolltolower="scrollToLower"
            refresher-enabled :refresher-triggered="refresherState" @refresherrefresh="bindrefresherrefresh"
            :scroll-top="scrollTop">
            <!-- 调试信息：list = {{list}}, list.length = {{list ? list.length : 'undefined'}} -->
            <template v-if="list && list.length > 0">
                <section v-for="(item, index) in list" :key="index">
                    <view class="list-section">
                        <view class="items" style="width:100%">
                            <view class="item_title" @click="details(item)">
                                <view class="title">{{ item.purchaseOrderCode }}</view>
                                <view class="status">{{ item.showStatusName }}</view>
                            </view>
                            <view class="item_content" @click="details(item)">
                                <p class="item">牛源地：<text class="text">{{ item.provinceName }}</text></p>
                                <p class="item">牛经纪：<text class="text">{{ item.brokerName }}</text></p>
                                <p class="item">运输信息：<text class="text">{{ item.driverName || '--' }} {{ item.driverName
                                    ? item.licensePlateNumber || '' : '' }}</text></p>
                                <p class="item" v-if="item.showStatus <= 20">预计发车日期：<text
                                        class="text">{{ item.deliveryStartTime }}</text></p>
                                <p class="item" v-else-if="item.showStatus <= 30">预计抵达日期：<text
                                        class="text">{{ item.deliveryEndTime }}</text></p>
                                <p class="item" v-else-if="item.showStatus <= 40">抵达日期：<text
                                        class="text">{{ item.arriveTime }}</text></p>
                                <p class="item" v-if="item.showStatus <= 10">预计发车数量：<text class="text">{{
                                        item.livestockNum || '--' }}头</text></p>
                                <p class="item" v-else-if="item.showStatus <= 30">实际发车数量：<text class="text">{{
                                        item.deliveryNumber }}头</text></p>
                                <p class="item" v-if="item.showStatus > 30">运转差额：<text class="text">{{
                                        item.transportLossWeight }}kg</text></p>
                                <p class="item" v-if="item.showStatus > 30">延迟时间：<text class="text">{{ item.delayHours
                                        }}小时</text></p>
                            </view>
                            <view class="list_btn_items">
                                <view class="btn_section">
                                    <view class="btn_item" @click="purchasePrepare(item)"
                                        v-if="$hasPermi('nmb:demandOrder:prepare') && item.showStatus == 10">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/beihuo.png"
                                            alt="">
                                        <text>备货</text>
                                    </view>
                                    <view class="btn_item" @click="confirmFn(item)"
                                        v-if="$hasPermi('nmb:purchaseOrder:go') && item.showStatus == 20">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/queren.png"
                                            alt="">
                                        <text>发车确认</text>
                                    </view>
                                    <view class="btn_item" @click="arrive(item)"
                                        v-if="$hasPermi('nmb:purchaseOrder:arrive') && item.showStatus == 30">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/deda.png"
                                            alt="">
                                        <text>抵达</text>
                                    </view>
                                    <view class="btn_item" @click="accept(item)"
                                        v-if="$hasPermi('nmb:purchaseOrder:acceptance') && item.showStatus == 40">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/yanshou.png"
                                            alt="">
                                        <text>验收</text>
                                    </view>
                                    <view class="btn_item" @click="sign(item)"
                                        v-if="$hasPermi('nmb:purchaseOrder:sign') && item.showStatus == 41">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/yanshou.png"
                                            alt="">
                                        <text>验收</text>
                                    </view>
                                    <view class="btn_item" @click="repayment(item)"
                                        v-if="$hasPermi('nmb:financeOrder:costFee:list')">
                                        <img class="icon"
                                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/yulebao.png"
                                            alt="">
                                        <text>费用管理</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
            <template v-else>
                <!-- 调试信息：isEmpty = {{isEmpty}}, list = {{list}} -->
                <!-- <u-empty text="暂无采购计划" mode="data" :icon-size='150'></u-empty> -->
                <nullList />
            </template>
            <div v-if="list && list.length > 0" :style="'height:' + (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
        <view class="container-footer"
            :style="'padding:' + (isIphonex ? '40rpx 60rpx;' : '15rpx 60rpx;') + 'background-color: #fff;'"
            v-if="$hasPermi('nmb:purchaseOrder:add')">
            <u-button hover-class='none' :custom-style="{
                'background-color': '#40CA8F',
                'color': 'white'
            }" shape="circle" @click="addPlan">新增计划</u-button>
        </view>
        <FilterList :pickerFilterShow="pickerFilterShow" @canel="close" @close="close" @submitForm="submitForm" />
        <u-modal v-model="showConfirm" content="确认后即将运转牛只，请确认" show-cancel-button @confirm="handleSubmit"
            @cancel="showConfirm = false" confirmColor="#40CA8F"></u-modal>
    </view>
</template>

<script>

import FilterList from "./filterList/index.vue"
import { purchaseOrderPage, confirm } from '@/api/pages/purchaseOrder'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import nullList from '@/components/null-list/index.vue'


export default {
    components: {
        FilterList,
        CustomNavbar,
        nullList
    },
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            list: [],
            pageNum: 1,
            pageSize: 10,
            searchText: '',
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            demandOrderId: '',
            paramsData: {},
            showConfirm: false,
            goType: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.demandOrderId = opation.demandOrderId
        this.goType = opation.goType
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) {
            uni.showLoading({
                title: '加载中',
                icon: 'none'
            })
            let params = {}
            if (val) {
                if (this.searchText) {
                    val.searchValue = this.searchText;
                    this.filters.searchValue = this.searchText;
                } else {
                    delete val.searchValue
                    delete this.filters.searchValue
                }
                params = this.searchText ? val : { ...this.filters, ...val }
            }
            purchaseOrderPage({
                pageNum: 1,
                pageSize: 10,
                ...params,
                demandOrderId: this.demandOrderId
            }).then(response => {
                if (response.code === 200) {
                    let list = response.result?.list || [];
                    console.log(list)
                    let total = parseInt(response.result?.total || 0);
                    this.paramsData = response.result.params
                    if (this.pageNum >= 2) {
                        this.list = this.list.concat(list);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    } else {
                        if (total >= 1) {
                            console.log('设置数据显示，total:', total, 'list:', list);
                            this.isEmpty = false;
                            this.list = list;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        } else {
                            console.log('设置空状态，total:', total);
                            this.isEmpty = true
                        }
                    }
                }
            })
            uni.hideLoading()
        },
        // 搜索
        fifterClick() {
            this.searchText = ''
            this.pickerFilterShow = true
        },
        handleSearch() {
            let params = this.getPamams();
            this.pageNum = 1;
            params.pageNum = this.pageNum
            params.searchValue = this.searchText;
            this.filters.pageNum = this.pageNum;
            this.filters.searchValue = this.searchText;
            this.getList(params);
        },
        submitForm(val) {
            this.pageNum = 1
            val.pageNum = this.pageNum;
            val.pageSize = this.pageSize;
            this.filters = val;
            this.list = [];
            this.getList(val);
            this.pickerFilterShow = false;
        },
        close() {
            this.pickerFilterShow = false;
        },
        scrollToLower() {
            if (this.noMore) return;
            this.pageNum++;
            this.isParams();
        },
        bindrefresherrefresh() {
            this.refresherState = true;
            this.pageNum = 1;
            this.noMore = false;
            this.isParams();
            setTimeout(() => {
                this.refresherState = false;
                this.$toast('刷新成功')
            }, 1000);
        },
        getPamams() {
            let params = {
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }
            return params
        },
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        details(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/purchase/detail?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        addPlan() {
            if (this.goType == 1) {
                uni.navigateTo({
                    url: '/myPackge1/pages/purchase/form'
                })
                return
            } else {
                uni.navigateTo({
                    url: '/myPackge1/pages/purchase/form?demandOrderId=' + this.demandOrderId
                })
            }
        },
        purchasePrepare(row) {
            uni.navigateTo({
                url: '/myPackge2/pages/catchingCows/index?purchaseOrderId=' + row.purchaseOrderId
            })
        },
        repayment(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/payment/index?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        confirmFn(item) {
            this.showConfirm = true
            this.currentItem = item
        },
        handleSubmit() {
            confirm({
                purchaseOrderId: this.currentItem.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    this.showConfirm = false
                    this.$toast('已发车')
                    this.getList()
                }
            })
        },
        arrive(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/arrive/index?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        detailList(item) {
            uni.navigateTo({
                url: `/myPackge1/pages/transport/detailList?purchaseOrderId=${item.purchaseOrderId}`
            })
        },
        accept(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/checkAccept/cowSource?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        sign(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/checkAccept/detail?purchaseOrderId=' + item.purchaseOrderId
            })
        }
    },
}
</script>

<style scoped lang="scss">
@import '@/common/css/listItem.scss';

.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/caigou.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.searchs_section {
    margin-top: 349rpx;
}

// .stats-section {
//     margin-top: -285rpx;
//     padding: 0 30rpx 30rpx;
//     display: flex;
//     justify-content: space-between;
//     background: #FFFFFF;
//     border-radius: 20rpx;
//     margin-left: 30rpx;
//     margin-right: 30rpx;
//     padding-top: 30rpx;
//     z-index: 99;
//     height: 186rpx;
// }

// .stats-card {
//     flex: 1;
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     text-align: center;

//     .stats-icon {
//         width: 60rpx;
//         height: 60rpx;
//         margin-bottom: 10rpx;

//         img {
//             width: 100%;
//             height: 100%;
//         }
//     }

//     .stats-number {
//         font-size: 32rpx;
//         font-weight: 600;
//         color: #1DB17A;
//         margin-bottom: 5rpx;
//     }

//     .stats-label {
//         font-size: 24rpx;
//         color: #666666;
//     }
// }
// 统计组件样式
.stats-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding: 40rpx 20rpx;
    margin: 30rpx;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    margin-top: -285rpx;
    z-index: 99;
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.stats-icon {
    position: relative;
    margin-bottom: 20rpx;

    image {
        width: 60rpx;
        height: 60rpx;
    }

    .stats-badge {
        position: absolute;
        top: -15rpx;
        right: -52rpx;
        background: linear-gradient(270deg, #6DD570 0%, #1CC370 100%);
        color: #FFFFFF;
        font-size: 24rpx;
        font-weight: 500;
        padding: 5rpx 17rpx;
        border-radius: 20rpx;
        height: 36rpx;
        min-width: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.stats-label {
    font-size: 24rpx;
    color: #333;
    text-align: center;
    line-height: 1.2;
    font-weight: 400;
}


.container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
}
</style>