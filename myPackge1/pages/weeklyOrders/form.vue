<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType"  label-width="auto" :label-style="labelStyle" label-position="top">
                    <u-form-item label="关联合同" required="true" prop="saleContractName" right-icon="arrow-right" v-if="!saleContractId">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.saleContractName" placeholder="请选择关联合同" disabled @click="contractPopupShow = true"/>
                    </u-form-item>
                    <u-form-item label="需求方公司名称">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="saleContractDataInfo.companyName" placeholder="请输入需求方公司名称" disabled/>
                    </u-form-item>
                    <u-form-item label="联系人" required="true" prop="customerContact" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.customerContact" placeholder="请输入联系人" disabled @click="showUserPicker = true"/>
                    </u-form-item>
                    <u-form-item label="交货时间" required="true" prop="deliveryEndTime">
                        <view class="time-view">
                            <view class="start-time" :style="deliveryStartTime.title === '起始时间' ? 'color:#999' : 'color:#222'"
                                @click="handleShowTime('deliveryStartTime')">
                                {{ deliveryStartTime.title }}
                            </view>
                            -
                            <view class="start-time" :style="deliveryEndTime.title === '结束时间' ? 'color:#999' : 'color:#222'"
                                @click="handleShowTime('deliveryEndTime')">
                                {{ deliveryEndTime.title }}
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="需求方所在区" right-icon="arrow-right">
                        <p :class="form.deliveryProvince ? '' : 'tips'" @click="pickerAreaShow = true">{{ form.deliveryProvince ? form.deliveryProvince + form.deliveryCity : '请选择需求方所在区' }}</p>
                    </u-form-item>
                    <u-form-item label="收货地址">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deliveryDetailAddress" placeholder="请输入收货地址"/>
                    </u-form-item>
                    <u-form-item label="活畜品种" required="true" prop="varietiesId">
                        <view class="job-type-content">
                            <p v-for="item in categoryList" :key="item.varietiesName"
                                :class="item.varietiesId == form.varietiesId ? 'policy-type-active' : ''"
                                @click="handleActive('varietiesId', item.varietiesId, 'varietiesName', item.varietiesName)">
                                {{ item.varietiesName }}
                            </p>
                        </view>
                    </u-form-item>
                    
                    <u-form-item label="品种分类" required="true" prop="categoryId">
                        <view class="job-type-content">
                            <p v-for="item in varietiesList" :key="item.categoryName"
                                :class="item.categoryId == form.categoryId ? 'policy-type-active' : ''"
                                @click="handleActive('categoryId', item.categoryId, 'categoryName', item.categoryName)">
                                {{ item.categoryName }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="活畜月龄" required="true" prop="ageRange">
                        <view class="job-type-content">
                            <p v-for="item in livestockAgeList" :key="item.dictLabel"
                                :class="item.dictLabel == form.ageRange ? 'policy-type-active' : ''"
                                @click="handleActive1('ageRange', item.dictLabel)">
                                {{ item.dictLabel }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="活畜重量" required="true" prop="weightRange">
                        <view class="job-type-content">
                            <p v-for="item in livestockWeightList" :key="item.dictLabel"
                                :class="item.dictLabel == form.weightRange ? 'policy-type-active' : ''"
                                @click="handleActive1('weightRange', item.dictLabel)">
                                {{ item.dictLabel }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="牛源地" right-icon="arrow-right" required="true" prop="provinceName">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.provinceName" placeholder="请输入牛源地" disabled @click="showPicker = true"/>
                    </u-form-item>
                    <u-form-item label="活牛单价(元/kg)" required="true" prop="unitPrice">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.unitPrice" placeholder="请输入活牛单价"/>
                    </u-form-item>
                    <u-form-item label="订单数量（头）"  prop="livestockNum">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.livestockNum" type="number" placeholder="请输入订单数量"/>
                    </u-form-item>
                    <u-form-item label="订单要求" required="true" prop="remark">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.remark" placeholder="请输入订单要求(200字)" type="textarea" :maxlength="200"/>
                    </u-form-item>
                </u-form>
            </view>
			<div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="addOrder">提交</u-button>
		</view>
		<u-select confirm-color='#40CA8F' v-model="showPicker" :list="provinceData" value-name="provinceId" label-name="provinceName" @cancel="showPicker = false" @confirm="submitPicker"></u-select>
		<u-select confirm-color='#40CA8F' v-model="showUserPicker" :list="userList" label-name="nickName" value-name="nmbUserId" @cancel="showUserPicker = false" @confirm="submitUserPicker"></u-select>
		<u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" @confirm="submitTime"></u-picker>
        <addressPicker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow" @addressCanel="pickerAreaShow = false" :titleShow="false" />
        <ContractList :contractPopupShow="contractPopupShow" @canel="contractPopupShow = false" @close="contractPopupShow = false" @selectContract="selectContract" />
    </view>
</template>

<script>
import {
    mapState
} from "vuex"
import {
    getDicts,
    livestockCategory,
    animalTypeList
} from "@/api/dict.js"
import { provinceList } from '@/api/common.js'
import { demandOrderAdd, nmbUserList } from '@/api/pages/demandOrder'
import { saleContractInfo } from '@/api/pages/salesContract'
import addressPicker from '@/components/address-picker/index.vue'
import ContractList from '../purchase/components/contractList.vue'
	export default {
		components: {
			ContractList,
            addressPicker
		},
		data() {
			return {
				showTime: false, //时间弹出选择框
				deliveryEndTime: {
					title: '结束时间',
					timestamp: 0
				}, 
				deliveryStartTime: {
					title: '起始时间',
					timestamp: 0
				},
				form: {
                    saleContractName: '',
                    saleContractId: '',
                    nmbCompanyId: '',
                    customerContactId: '',
                    customerContact: '',
                    deliveryStartTime: '',
                    deliveryEndTime: '',
                    deliveryDetailAddress: '',
                    varietiesId: '',
                    varietiesName:  '',
                    categoryId: '',
                    categoryName:  '',
                    ageRange:  '',
                    weightRange:  '',
                    provinceId: '',
                    provinceName:  '',
                    unitPrice: '',
                    livestockNum: '',
                    remark: ''
                },
                customStyle: { fontSize: '26rpx' },
                labelStyle: { color: '#333', fontSize: '26rpx' },
                placeholderStyle: 'color:#999;font-size: 26rpx;',
                pickerAreaShow: false,
				showPicker: false,
                showUserPicker: false,
                contractPopupShow: false,
				typeTime: '',
                varietiesList: [],
				categoryList: [],
				livestockAgeList: [],
				livestockWeightList: [],
                isIphonex: getApp().globalData.systemInfo.isIphonex,
                showPicker: false,
                provinceData: [],
                errorType: ['message'],
                rules: {
                    saleContractName: [
                        { required: true, message: '请选择关联合同', trigger: 'blur' },
                    ],
                    nmbCompanyId: [
                        { required: true, message: '请输入需求方公司名称', trigger: 'blur' },
                    ],
                    customerContact: [
                        { required: true, message: '请输入联系人', trigger: 'blur' },
                    ],
                    deliveryEndTime: [
                        { required: true, message: '请选择交货时间', trigger: 'change' },
                    ],
                    deliveryEndTime: [
                        { required: true, message: '请输入收货地址', trigger: 'blur' },
                    ],
                    unitPrice: [
                        { required: true, message: '请输入活牛单价', trigger: 'blur' }
                    ],
                    varietiesId: [
                        { required: true, message: '请选择活畜品种', trigger: 'blur' },
                    ],
                    categoryId: [
                        { required: true, message: '请选择品种分类', trigger: 'blur' },
                    ],
                    ageRange: [
                        { required: true, message: '请选择活畜月龄', trigger: 'blur' },
                    ],
                    weightRange: [
                        { required: true, message: '请选择活畜重量', trigger: 'blur' },
                    ],
                    remark: [
                        { required: true, message: '请输入采购要求', trigger: 'blur' },
                    ],
                    provinceName: [
                        { required: true, message: '请选择牛源地', trigger: 'blur' },
                    ],
                },
                saleContractId: '',
                saleContractDataInfo: {},
                userList: []
			}
		},
		computed: {
			...mapState({
				userInfo: (state) => state.userDetail.user,
			}),
		},
        onReady() {
            this.$refs.uForm.setRules(this.rules)
        },
        mounted() {
			livestockCategory({
				pageNum: 1,
				pageSize: 100000,
                categoryType: '403292860613267456'
			}).then(res => {
				this.categoryList = res.result || []
			})
			animalTypeList({
				pageSize: 9999,
				pageNum: 1,
                categoryType: '403292860613267456'
			}).then(res => {
				this.varietiesList = res.result || []
			})
            getDicts('livestock_age').then(res => {
				this.livestockAgeList = res.data || []
			})
            getDicts("livestock_weight").then((res) => {
                this.livestockWeightList = res.data || []
            })
        },
        onLoad(opation) {
            // 接收路由参数
            if (opation.saleContractId) {
                this.saleContractId = opation.saleContractId
                this.getInfo(opation.saleContractId)
                this.form.saleContractName = 'HT'
            }
			this.getProvinceList()
        },
		methods: {
            getInfo(saleContractId) {
                saleContractInfo({
                    saleContractId
                }).then(res => {
                    this.saleContractDataInfo = res.result
                    this.form.deliveryDetailAddress = res.result.detailAddress
                    this.form.deliveryProvince = res.result.province || ''
                    this.form.deliveryProvinceId = res.result.provinceId || ''
                    this.form.deliveryCity = res.result.city || ''
                    this.form.deliveryCityId = res.result.cityId || ''
                    this.form.nmbCompanyId = res.result.nmbCompanyId,
                    this.form.saleContractId = res.result.saleContractId
                    this.getUserList()
                })
            },
			getProvinceList() { // 省会列表
				provinceList({}).then(res => {
					this.provinceData = res.result
				})
			},
			selectContract(val) {
				this.form.saleContractName = val.saleContractCode
				this.form.saleContractId = val.saleContractId
                this.getInfo(val.saleContractId)
				this.contractPopupShow = false
			},
            getUserList() {
                nmbUserList({
                    userType: 2,
                    pageNum: 1,
                    pageSize: 200,
                    nmbCompanyId: this.saleContractDataInfo.nmbCompanyId
                }).then(res => {
					this.userList = res.result.list
				})
            },
            submitAddress(val) {
                this.form.province = val.areaName
                let areaName = val.areaName.split('-')
                let areaId = val.areaValue.split(',')
                this.form.deliveryProvince = areaName[0] || ''
                this.form.deliveryProvinceId = areaId[0] || ''
                this.form.deliveryCity = areaName[1] || ''
                this.form.deliveryCityId = areaId[1] || ''
            },
			submitTime(val) {
				const arr = ['deliveryStartTime', 'deliveryEndTime']
				arr.map((item, index) => {
					if (item === this.typeTime) {
						this[item].timestamp = val.timestamp
						if (index === 0 || index === 1) {
							if (this.deliveryStartTime.timestamp > this.deliveryEndTime.timestamp && this.deliveryEndTime.timestamp !== 0) {
								uni.showToast({
									title: '结束时间不能小于起始时间',
									icon: 'none'
								})
								return false
							}
						}
						this[item].title = val.year + '-' + val.month + '-' + val.day
						this.form[item] = val.year + '-' + val.month + '-' + val.day
					}
				})
			},
			handleShowTime(val) {
				if (this.deliveryStartTime.timestamp === 0 && val === 'deliveryEndTime') {
					uni.showToast({
						title: '请选择起始日期',
						icon: 'none'
					})
					return
				}
				this.typeTime = val
				this.showTime = true
			},
			submitPicker(val) {
                const values = val[0]
                this.form.provinceName = values.label
                this.form.provinceId = values.value
				this.showPicker = false
			},
			submitUserPicker(val) {
                const values = val[0]
                this.form.customerContact = values.label
                this.form.customerContactId = values.value
				this.showUserPicker = false
			},
            handleActive(key, value, lable, labelValue) {
				this.form[key] = value
				this.form[lable] = labelValue
			},
            handleActive1(key, value) {
				this.form[key] = value
			},
            addOrder() {
                this.$refs.uForm.validate((valid) => {
                    if (valid) {
                        demandOrderAdd({
                            ...this.form,
                            saleContractName: ''
                        }).then((res) => {
                            if (res.code == 200) {
                                uni.showToast({
                                    title: '新建成功',
                                    icon: 'none'
                                })
                                uni.navigateBack({
                                    delta:1
                                })
                            }
                        })
                    }
                })
            },
		}
	}
</script>

<style lang="less" scoped>

	.regulatory-area {
		padding: 29rpx;

		.regulatory-area-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

	}


    .container {
        margin: 30rpx;
        background: #fff;
        box-sizing: border-box;
        padding: 30rpx 32rpx 40rpx;
        border-radius: 30rpx;

		.time-view {
			display: flex;
			justify-content: space-between;
			line-height: 50rpx;

			.start-time {
				width: 50%;
				text-align: center;
				color: #999;
			}

		}
        /deep/ .u-form-item {
            padding: 20rpx 20rpx !important;
        }

        .tips {
            font-size: 28rpx;
            color: #999;
        }

        .voucher {
            padding-bottom: 40rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }

        .all-img {
            image {
                width: 154rpx;
                height: 154rpx;
                background: #d8d8d8;
                border-radius: 16rpx;
                margin-right: 20rpx;
            }
        }

        .manyMode {
            background: transparent;
            height: auto;
        }

        .uploadImage {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            // align-content: space-between;
            position: relative;

            .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }
        }

        .item {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        border: 2rpx dashed #d8d8d8;

        .uploadIcon {
            width: 100%;
            height: 120rpx;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d8d8d8;
            background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
            background-size: 20rpx 20rpx;
            background-position: center 30rpx;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            z-index: 5;
        }
        }
    }
}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
    .scroll-view{
        background-color: #fff;
    }
    .submit{
        width: 100%;
        height: 80rpx;
        color: #fff;
        text-align: center;
        line-height: 80rpx;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-top: 40rpx;
        view{
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            background-color: #40CA8F;
        }
    }
    
    .job-type-content {
        display: flex;
        flex-wrap: wrap;
        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 0px 35rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 15rpx 30rpx 15rpx 0;
            border: 2rpx solid transparent;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
    /deep/ .u-btn-picker--primary{
        color: #40CA8F !important;
    }
</style>