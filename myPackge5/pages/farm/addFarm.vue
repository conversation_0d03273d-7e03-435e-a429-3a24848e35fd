<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
                    <u-form-item label="养殖场名称" required prop="pastureName">
                        <u-input v-model="form.pastureName" maxlength="10" placeholder="请输入养殖场名称，10个字以内"
                            :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
                    </u-form-item>
                    <u-form-item label="养殖场性质" required prop="pastureNature"
                        :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getNatureText(form.pastureNature)" placeholder="请选择养殖场性质" disabled
                            @click="!isDetail && (showNatureSelect = true)" />
                    </u-form-item>
                    <u-form-item label="养殖场位置" required prop="provinceName" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="form.provinceName ? `${form.provinceName}-${form.cityName}-${form.countyName}` : ''"
                            placeholder="请选择养殖场位置" disabled @click="!isDetail && (pickerAreaShow = true)" />
                    </u-form-item>
                    <u-form-item label="详细地址" required prop="address">
                        <u-input v-model="form.address" placeholder="请输入详细地址" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" :disabled="isDetail" />
                    </u-form-item>

                    <!-- 养殖数量 -->
                    <u-form-item label="养殖数量" required prop="breedingQuantity">
                        <u-input v-model="form.breedingQuantity" placeholder="请输入养殖数量" type="number"
                            :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
                    </u-form-item>
                </u-form>
                <view class="cultivate-section">
                    <view class="section-title section-type">养殖方式：</view>
                    <view class="cultivate-options">
                        <view v-for="item in cultivateList" :key="item.value"
                            :class="['cultivate-item', { 'active': form.cultivateType === item.value }]"
                            @click="!isDetail && selectCultivateType(item.value)">
                            {{ item.label }}
                        </view>
                    </view>
                </view>
            </view>
            <view class="upload-section">
                <view class="section-title">
                    养殖场照片：
                    <img v-if="!isDetail" class="upload-icon" src="../../icon/photo.png"
                        @click="uploadImage('farmImages', 3)"></img>
                </view>
                <view class="section-subtitle">最多不超过3张，单张图片小于2M</view>

                <view class="uploadImage" v-if="form.farmImages.length">
                    <view class="itemAlready" v-for="(item, index) in form.farmImages" :key="index">
                        <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
                        <view v-if="!isDetail" class="closeIcon" @click="deleteImage(index, 'farmImages')"></view>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">{{ isEdit ? '保存' : '确认' }}</view>
        </view>

        <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow"
            @addressCanel="pickerAreaShow = false" :titleShow="false" :showLevel3="true" />

        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showNatureSelect" mode="single-column"
            :list="natureList" label-name="label" @confirm="selectNature" value-name="value" />
    </view>
</template>

<script>
import addressPicker from '@/components/address-picker/index.vue'
import { pastureAdd, pastureEdit, pastureDetail } from '@/api/pages/livestock/farm'
import { getDicts } from "@/api/dict.js"
import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    name: 'addFarm',
    components: {
        addressPicker,
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            // 页面状态
            pastureId: '', // 养殖场ID
            isEdit: false, // 是否编辑模式
            isDetail: false, // 是否详情模式
            form: {
                pastureName: '',
                address: '',
                pastureNature: '',
                cultivateType: '',
                farmImages: [],
                provinceName: '',
                provinceId: '',
                cityName: '',
                cityId: '',
                countyName: '',
                countyId: ''
            },
            pickerAreaShow: false,
            showNatureSelect: false,
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            isSubmitting: false,
            natureList: [],
            cultivateList: [],
            rules: {
                pastureName: [{
                    required: true,
                    message: '请输入养殖场名称',
                    trigger: ['blur', 'change']
                }],
                provinceName: [{
                    required: true,
                    message: '请选择养殖场位置',
                    trigger: ['blur', 'change']
                }],
                address: [{
                    required: true,
                    message: '请输入详细地址',
                    trigger: ['blur', 'change']
                }],
                pastureNature: [{
                    required: true,
                    message: '请选择养殖场性质',
                    trigger: ['blur', 'change']
                }],
                cultivateType: [{
                    required: true,
                    message: '请选择养殖方式',
                    trigger: ['blur', 'change']
                }],
                farmImages: [{
                    required: true,
                    validator: (rule, value, callback) => {
                        value?.length > 0 ? callback() : callback(new Error('请上传养殖场照片'))
                    },
                    message: '请上传养殖场照片',
                    trigger: 'change'
                }]
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        },
        pageTitle() {
            if (this.isDetail) return '养殖场详情';
            if (this.isEdit) return '编辑养殖场';
            return '新建养殖场';
        },
        isFormValid() {
            const { pastureName, provinceName, address, pastureNature, cultivateType, farmImages } = this.form
            return pastureName && provinceName && address && pastureNature !== '' && cultivateType !== '' && farmImages.length > 0
        }
    },

    onLoad(options) {
        // 判断页面模式
        if (options.pastureId) {
            this.pastureId = options.pastureId;
            this.isEdit = options.mode === 'edit';
            this.isDetail = options.mode === 'detail';
        }

        this.loadNatureDict();
        this.loadCultivateDict();

        // 编辑或详情
        if (this.pastureId) {
            this.loadFarmDetail();
        }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        async loadNatureDict() {
            try {
                const res = await getDicts('pasture_nature');
                if (res && res.data) {
                    this.natureList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载字典失败:', error);
                this.$toast('加载字典失败');
            }
        },

        async loadCultivateDict() {
            try {
                const res = await getDicts('cultivate_type');
                if (res && res.data) {
                    this.cultivateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载养殖方式字典失败:', error);
            }
        },

        // 加载养殖场详情
        async loadFarmDetail() {
            try {
                const res = await pastureDetail({ pastureId: this.pastureId });
                if (res.code === 200 && res.result) {
                    const data = res.result;
                    this.form = {
                        pastureName: data.pastureName || '',
                        address: data.address || '',
                        breedingQuantity: data.breedingQuantity || '',
                        pastureNature: data.pastureNature || '',
                        cultivateType: data.cultivateType || '',
                        urls: data.farmImages ? data.farmImages.split(',').filter(img => img) : [],
                        provinceName: data.provinceName || '',
                        provinceId: data.provinceId || '',
                        cityName: data.cityName || '',
                        cityId: data.cityId || '',
                        countyName: data.countyName || '',
                        countyId: data.countyId || ''
                    };
                } else {
                    throw new Error(res.message || '获取详情失败');
                }
            } catch (error) {
                console.error('加载养殖场详情失败:', error);
                this.$toast('加载详情失败');
                uni.navigateBack();
            }
        },

        getNatureText(value) {
            const nature = this.natureList.find(item => item.value === value)
            return nature ? nature.label : ''
        },

        submitAddress(val) {
            if (!val?.areaName || !val?.areaValue) return

            const areaName = val.areaName.split('-')
            const areaId = val.areaValue.split(',')

            this.form = {
                ...this.form,
                provinceName: areaName[0] || '',
                provinceId: areaId[0] || '',
                cityName: areaName[1] || '',
                cityId: areaId[1] || '',
                countyName: areaName[2] || '',
                countyId: areaId[2] || ''
            }

            this.pickerAreaShow = false
            this.resetField('provinceName')
        },

        // 选择养殖场性质
        selectNature(value) {
            if (!value?.length) return
            this.form.pastureNature = value[0].value
            this.showNatureSelect = false
            this.resetField('pastureNature')
        },

        selectCultivateType(value) {
            this.form.cultivateType = value
            this.resetField('cultivateType')
        },

        // 上传图片
        uploadImage(type, maxCount) {
            if (this.form[type].length >= maxCount) {
                this.$toast(`图片最多只能上传${maxCount}张`)
                return
            }
            const that = this
            uni.chooseImage({
                count: maxCount - this.form[type].length,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: function (res) {
                    res.tempFilePaths.forEach(filePath => {
                        uploadFiles({
                            filePath: filePath,
                        }).then((data) => {
                            that.form[type].push(data)
                            that.resetField(type)
                        }).catch(error => {
                            console.error('上传失败:', error)
                            that.$toast('上传失败')
                        })
                    })
                },
                fail(e) {
                    console.error('选择图片失败:', e)
                },
            })
        },

        // 预览图片
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },

        // 删除图片
        deleteImage(index, type) {
            this.form[type].splice(index, 1)
            this.resetField(type)
            this.$forceUpdate()
        },

        // 提交表单
        async submitForm() {
            if (this.isSubmitting) return
            if (!this.isFormValid) {
                return this.$toast('请填写完整信息')
            }

            try {
                this.isSubmitting = true
                const valid = await this.validateForm()
                if (!valid) return

                // 准备提交数据
                const submitData = {
                    ...this.form,
                    urls: this.form.farmImages
                };

                // 如果是编辑模式，添加ID
                if (this.isEdit) {
                    submitData.pastureId = this.pastureId;
                }

                const res = this.isEdit
                    ? await pastureEdit(submitData)
                    : await pastureAdd(submitData);

                if (res.code === 200) {
                    uni.$emit('updateFarmList')
                    this.$toast(this.isEdit ? '保存成功' : '添加成功')
                    uni.navigateBack({ delta: 1 })
                } else {
                    throw new Error(res.message || '提交失败')
                }
            } catch (error) {
                this.handleError(error, '提交失败')
            } finally {
                this.isSubmitting = false
            }
        },

        validateForm() {
            return new Promise(resolve => {
                this.$refs.uForm.validate(valid => resolve(valid))
            })
        },

        handleError(error, customMessage = '') {
            console.error(error)
            this.$toast(error.message || customMessage || '操作失败')
        },

        resetField(value) {
            if (!value) return
            this.$refs.uForm?.fields?.forEach(field => {
                if (field.prop === value) {
                    field.resetField()
                }
            })
        }
    },
}
</script>

<style lang="less" scoped>
	@import url('../../css/index.less');
</style>
