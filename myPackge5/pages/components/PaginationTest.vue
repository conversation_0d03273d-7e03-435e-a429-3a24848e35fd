<template>
    <view class="test-page">
        <view class="title">分页组件测试</view>
        
        <!-- 测试不同总数的分页 -->
        <view class="test-section">
            <view class="section-title">测试1: 总共8页 (80条数据)</view>
            <CommonPagination 
                :current="test1.current"
                :page-size="test1.pageSize"
                :total="test1.total"
                @change="handleTest1Change"
            />
            <view class="info">
                当前页: {{ test1.current }}, 
                总页数: {{ Math.ceil(test1.total / test1.pageSize) }}
            </view>
        </view>
        
        <view class="test-section">
            <view class="section-title">测试2: 总共15页 (150条数据)</view>
            <CommonPagination 
                :current="test2.current"
                :page-size="test2.pageSize"
                :total="test2.total"
                @change="handleTest2Change"
            />
            <view class="info">
                当前页: {{ test2.current }}, 
                总页数: {{ Math.ceil(test2.total / test2.pageSize) }}
            </view>
        </view>
        
        <view class="test-section">
            <view class="section-title">测试3: 总共3页 (30条数据)</view>
            <CommonPagination 
                :current="test3.current"
                :page-size="test3.pageSize"
                :total="test3.total"
                @change="handleTest3Change"
            />
            <view class="info">
                当前页: {{ test3.current }}, 
                总页数: {{ Math.ceil(test3.total / test3.pageSize) }}
            </view>
        </view>
    </view>
</template>

<script>
import CommonPagination from './CommonPagination.vue'

export default {
    name: 'PaginationTest',
    components: {
        CommonPagination
    },
    data() {
        return {
            test1: {
                current: 1,
                pageSize: 10,
                total: 80
            },
            test2: {
                current: 1,
                pageSize: 10,
                total: 150
            },
            test3: {
                current: 1,
                pageSize: 10,
                total: 30
            }
        }
    },
    methods: {
        handleTest1Change(page) {
            console.log('测试1 - 页码变化:', page);
            this.test1.current = page;
        },
        handleTest2Change(page) {
            console.log('测试2 - 页码变化:', page);
            this.test2.current = page;
        },
        handleTest3Change(page) {
            console.log('测试3 - 页码变化:', page);
            this.test3.current = page;
        }
    }
}
</script>

<style lang="scss" scoped>
.test-page {
    padding: 40rpx;
    background: #f5f5f5;
    min-height: 100vh;
    
    .title {
        font-size: 36rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 60rpx;
        color: #333;
    }
    
    .test-section {
        background: #fff;
        border-radius: 16rpx;
        padding: 40rpx;
        margin-bottom: 40rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
        
        .section-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 30rpx;
        }
        
        .info {
            margin-top: 20rpx;
            font-size: 28rpx;
            color: #666;
            text-align: center;
        }
    }
}
</style>
