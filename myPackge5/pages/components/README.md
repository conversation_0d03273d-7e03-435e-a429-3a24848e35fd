# 通用组件使用说明

## CommonTable 通用表格组件

### 基本用法

```vue
<template>
    <CommonTable 
        :columns="tableColumns" 
        :data="tableData"
        @row-click="handleRowClick"
    />
</template>

<script>
import CommonTable from '../components/CommonTable.vue'

export default {
    components: {
        CommonTable
    },
    data() {
        return {
            tableColumns: [
                { title: '姓名', key: 'name', width: '120rpx', align: 'center' },
                { title: '年龄', key: 'age', width: '100rpx', align: 'center' },
                { title: '地址', key: 'address', align: 'left' }
            ],
            tableData: [
                { name: '张三', age: 25, address: '北京市朝阳区' },
                { name: '李四', age: 30, address: '上海市浦东新区' }
            ]
        }
    },
    methods: {
        handleRowClick(row, index) {
            console.log('点击行:', row, index);
        }
    }
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| columns | Array | [] | 表格列配置 |
| data | Array | [] | 表格数据 |
| border | Boolean | true | 是否显示边框 |
| stripe | Boolean | false | 是否显示斑马纹 |

### columns 列配置

| 参数 | 类型 | 说明 |
|------|------|------|
| title | String | 列标题 |
| key | String | 对应数据字段名 |
| width | String | 列宽度，如 '120rpx' |
| align | String | 对齐方式：'left'、'center'、'right' |
| slot | String | 自定义插槽名称 |

### Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| row-click | 行点击事件 | (row, index) |

### 自定义列内容

```vue
<CommonTable :columns="columns" :data="data">
    <template #status="{ row, column, index }">
        <view class="status" :class="row.status">
            {{ row.status === 'active' ? '激活' : '禁用' }}
        </view>
    </template>
</CommonTable>
```

## CommonPagination 通用分页组件

### 基本用法

```vue
<template>
    <CommonPagination 
        :current="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @change="handlePageChange"
    />
</template>

<script>
import CommonPagination from '../components/CommonPagination.vue'

export default {
    components: {
        CommonPagination
    },
    data() {
        return {
            pagination: {
                current: 1,
                pageSize: 10,
                total: 100
            }
        }
    },
    methods: {
        handlePageChange(page) {
            this.pagination.current = page;
            // 重新加载数据
            this.loadData();
        }
    }
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| current | Number | 1 | 当前页码 |
| pageSize | Number | 10 | 每页条数 |
| total | Number | 0 | 总条数 |
| showPageCount | Number | 5 | 显示的页码按钮数量 |

### Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| change | 页码改变事件 | (page) |

## 完整示例

参考 `myPackge5/pages/underCare/inventoryDetails.vue` 文件中的完整实现。

### 主要功能

1. **数据加载**: 支持异步数据加载和模拟数据
2. **分页**: 自动处理分页逻辑
3. **筛选**: 结合筛选条件重新加载数据
4. **响应式**: 适配不同屏幕尺寸

### API 集成

在实际项目中，将 `generateMockData()` 方法替换为真实的API调用：

```javascript
async fetchInventoryData(params) {
    const response = await uni.request({
        url: '/api/inventory/list',
        method: 'GET',
        data: params
    });
    return response.data;
}
```

### 样式定制

可以通过 `:deep()` 选择器来自定义组件样式：

```scss
:deep(.common-table) {
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

:deep(.common-pagination) {
    background: transparent;
}
```
