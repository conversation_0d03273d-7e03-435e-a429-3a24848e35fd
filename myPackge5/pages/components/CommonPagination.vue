<template>
    <view class="common-pagination">
        <!-- 分页 -->
        <view class="pagination-controls">
            <!-- 上一页码组 -->
            <view class="page-btn prev-btn" :class="{ disabled: !canPrevGroup }" @click="handlePrevPage">
                <text>‹</text>
            </view>

            <!-- 页码 -->
            <view class="page-numbers">
                <view v-for="page in visiblePages" :key="page" class="page-btn page-number"
                    :class="{ active: page === currentPage }" @click="handlePageClick(page)">
                    <text>{{ page }}</text>
                </view>
            </view>

            <!-- 下一页码组 -->
            <view class="page-btn next-btn" :class="{ disabled: !canNextGroup }" @click="handleNextPage">
                <text>›</text>
            </view>
        </view>
        <view class="total-info">
            <text>共{{ total }}条</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CommonPagination',
    props: {
        // 当前页码
        current: {
            type: Number,
            default: 1
        },
        // 每页条数
        pageSize: {
            type: Number,
            default: 10
        },
        // 总条数
        total: {
            type: Number,
            default: 0
        },
        // 显示的页码按钮数量
        showPageCount: {
            type: Number,
            default: 5
        }
    },
    data() {
        return {
            currentPage: this.current,
            currentGroup: 1 // 当前页码组
        }
    },
    computed: {
        // 总页数
        totalPages() {
            return Math.ceil(this.total / this.pageSize);
        },
        // 可见的页码数组
        visiblePages() {
            const total = this.totalPages;
            const showCount = this.showPageCount;

            if (total <= showCount) {
                // 总页数小于等于显示页数，显示所有页码
                return Array.from({ length: total }, (_, i) => i + 1);
            }

            // 计算当前页码组的起始和结束页码
            const start = (this.currentGroup - 1) * showCount + 1;
            const end = Math.min(start + showCount - 1, total);

            const pages = [];
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            return pages;
        },
        // 总页码组数
        totalGroups() {
            return Math.ceil(this.totalPages / this.showPageCount);
        },
        // 是否可以向左切换页码组
        canPrevGroup() {
            return this.currentGroup > 1;
        },
        // 是否可以向右切换页码组
        canNextGroup() {
            return this.currentGroup < this.totalGroups;
        }
    },
    watch: {
        current(newVal) {
            this.currentPage = newVal;
            // 根据当前页码计算应该在哪个页码组
            this.currentGroup = Math.ceil(newVal / this.showPageCount);
        }
    },
    methods: {
        // 上一页码组
        handlePrevPage() {
            if (this.canPrevGroup) {
                this.currentGroup--;
                // 切换到新页码组的第一页
                const newPage = (this.currentGroup - 1) * this.showPageCount + 1;
                this.changePage(newPage);
            }
        },
        // 下一页码组
        handleNextPage() {
            if (this.canNextGroup) {
                this.currentGroup++;
                // 切换到新页码组的第一页
                const newPage = (this.currentGroup - 1) * this.showPageCount + 1;
                this.changePage(newPage);
            }
        },
        // 页码点击
        handlePageClick(page) {
            if (page !== this.currentPage) {
                this.changePage(page);
            }
        },
        // 切换页码
        changePage(page) {
            this.currentPage = page;
            this.$emit('change', page);
        }
    }
}
</script>

<style lang="scss" scoped>
.common-pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    margin: 30rpx;

    .total-info {
        margin-top: 30rpx;

        text {
            font-size: 26rpx;
            color: #25B47E;
        }
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .page-btn {
            min-width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dcdfe6;
            border-radius: 8rpx;
            background: #fff;
            cursor: pointer;
            // transition: all 0.3s;

            text {
                font-size: 28rpx;
                color: #606266;
            }

            &:hover:not(.disabled) {
                border-color: #25B47E;
                color: #25B47E;

                text {
                    color: #25B47E;
                }
            }

            &.active {
                background: #25B47E;
                border-color: #25B47E;

                text {
                    color: #fff;
                }
            }

            &.disabled {
                background: #f5f7fa;
                border-color: #e4e7ed;
                cursor: not-allowed;

                text {
                    color: #c0c4cc;
                }
            }


        }

        .page-numbers {
            display: flex;
            align-items: center;
            gap: 16rpx;
        }

        .prev-btn,
        .next-btn {
            width: 60rpx;

            text {
                font-size: 32rpx;
                font-weight: bold;
            }
        }
    }
}
</style>
