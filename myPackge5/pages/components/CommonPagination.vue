<template>
    <view class="common-pagination">
        <!-- 分页 -->
        <view class="pagination-controls">
            <!-- 上一页 -->
            <view class="page-btn prev-btn" :class="{ disabled: currentPage <= 1 }" @click="handlePrevPage">
                <text>‹</text>
            </view>

            <!-- 页码 -->
            <view class="page-numbers">
                <view v-for="page in visiblePages" :key="page" class="page-btn page-number"
                    :class="{ active: page === currentPage, ellipsis: page === '...' }" @click="handlePageClick(page)">
                    <text>{{ page }}</text>
                </view>
            </view>

            <!-- 下一页 -->
            <view class="page-btn next-btn" :class="{ disabled: currentPage >= totalPages }" @click="handleNextPage">
                <text>›</text>
            </view>
        </view>
        <view class="total-info">
            <text>共{{ total }}条</text>
        </view>
    </view>
</template>

<script>
export default {
    name: 'CommonPagination',
    props: {
        // 当前页码
        current: {
            type: Number,
            default: 1
        },
        // 每页条数
        pageSize: {
            type: Number,
            default: 10
        },
        // 总条数
        total: {
            type: Number,
            default: 0
        },
        // 显示的页码按钮数量
        showPageCount: {
            type: Number,
            default: 5
        }
    },
    data() {
        return {
            currentPage: this.current
        }
    },
    computed: {
        // 总页数
        totalPages() {
            return Math.ceil(this.total / this.pageSize);
        },
        // 可见的页码数组
        visiblePages() {
            const total = this.totalPages;
            const current = this.currentPage;
            const showCount = this.showPageCount;

            if (total <= showCount) {
                // 总页数小于等于显示页数，显示所有页码
                return Array.from({ length: total }, (_, i) => i + 1);
            }

            const pages = [];
            const half = Math.floor(showCount / 2);

            // 计算起始和结束页码
            let start = Math.max(1, current - half);
            let end = Math.min(total, start + showCount - 1);

            // 调整起始页码
            if (end - start + 1 < showCount) {
                start = Math.max(1, end - showCount + 1);
            }

            // 添加第一页和省略号
            if (start > 1) {
                pages.push(1);
                if (start > 2) {
                    pages.push('...');
                }
            }

            // 添加中间页码
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            // 添加省略号和最后一页
            if (end < total) {
                if (end < total - 1) {
                    pages.push('...');
                }
                pages.push(total);
            }

            return pages;
        }
    },
    watch: {
        current(newVal) {
            this.currentPage = newVal;
        }
    },
    methods: {
        // 上一页
        handlePrevPage() {
            if (this.currentPage > 1) {
                this.changePage(this.currentPage - 1);
            }
        },
        // 下一页
        handleNextPage() {
            if (this.currentPage < this.totalPages) {
                this.changePage(this.currentPage + 1);
            }
        },
        // 页码点击
        handlePageClick(page) {
            if (page !== '...' && page !== this.currentPage) {
                this.changePage(page);
            }
        },
        // 切换页码
        changePage(page) {
            this.currentPage = page;
            this.$emit('change', page);
        }
    }
}
</script>

<style lang="scss" scoped>
.common-pagination {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;
    margin: 30rpx;

    .total-info {
        margin-bottom: 30rpx;

        text {
            font-size: 28rpx;
            color: #909399;
        }
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .page-btn {
            min-width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dcdfe6;
            border-radius: 8rpx;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s;

            text {
                font-size: 28rpx;
                color: #606266;
            }

            &:hover:not(.disabled):not(.ellipsis) {
                border-color: #409eff;
                color: #409eff;

                text {
                    color: #409eff;
                }
            }

            &.active {
                background: #409eff;
                border-color: #409eff;

                text {
                    color: #fff;
                }
            }

            &.disabled {
                background: #f5f7fa;
                border-color: #e4e7ed;
                cursor: not-allowed;

                text {
                    color: #c0c4cc;
                }
            }

            &.ellipsis {
                border: none;
                background: transparent;
                cursor: default;

                text {
                    color: #c0c4cc;
                }
            }
        }

        .page-numbers {
            display: flex;
            align-items: center;
            gap: 16rpx;
        }

        .prev-btn,
        .next-btn {
            width: 60rpx;

            text {
                font-size: 32rpx;
                font-weight: bold;
            }
        }
    }
}
</style>
