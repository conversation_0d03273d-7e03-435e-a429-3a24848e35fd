<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="section-title">基础信息</view>
            <view class="container base-container">
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="耳标号" required prop="earTagNo" :border-bottom="false">
                            <u-input v-model="form.earTagNo" placeholder="请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" @blur="searchLivestock" />
                        </u-form-item>
                    </u-form>
                </view>
            </view>
            <view class="section-title second-section-title">免疫信息</view>
            <view class="container base-container">
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="免疫日期：" required prop="operateTime" class="operateTime">
                            <text :class="form.operateTime ? 'common' : 'tips'" @click="!isDetail && showDatePicker()"
                                style="text-align: right; font-size: 26rpx;">
                                {{ form.operateTime || "请选择" }}
                            </text>
                        </u-form-item>
                        <u-form-item label="疫苗名称：" required prop="vaccineName">
                            <u-input v-model="form.vaccineName" placeholder="请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="接种剂量：" required prop="vaccineDose">
                            <u-input v-model="form.vaccineDose" placeholder="请输入疫苗剂量（ml或g）" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="接种方式：" required prop="operateType" :border-bottom="false"
                            class="operateType">
                            <view class="cultivate-section">
                                <view class="cultivate-options">
                                    <view v-for="item in vaccinationList" :key="item.value"
                                        :class="['cultivate-item', { 'active': form.operateType === item.value }]"
                                        @click="!isDetail && selectOperateType(item.value)">
                                        {{ item.label }}
                                    </view>
                                </view>
                            </view>
                        </u-form-item>
                        <u-form-item label="疫苗厂家：" prop="company">
                            <u-input v-model="form.company" placeholder="选填，请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                        <u-form-item label="疫苗批次：" prop="vaccineBatches" :border-bottom="false">
                            <u-input v-model="form.vaccineBatches" placeholder="选填，请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" />
                        </u-form-item>
                    </u-form>
                </view>
            </view>
            <view class="section-title">免疫说明</view>
            <view class="container base-container">
                <view class="section">
                    <u-form :model="form" ref="uForm2" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-input v-model="form.remark" placeholder="选填，请描述一下免疫说明，200字以内" type="textarea"
                            :placeholder-style="placeholderStyle" maxlength="200" :disabled="isDetail" />
                    </u-form>
                </view>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F" range-color="#40CA8F"
            btn-type="success" v-model="showData" :mode="`date`" @change='changeData' :max-date='maxdata'
            :min-date="mindata"></u-calendar>
    </view>
</template>

<script>
import { selectEarTagNo, diseaseAdd } from '@/api/pages/livestock/underCare'
import CustomNavbar from '../components/CustomNavbar.vue'
import { getDicts } from "@/api/dict.js"

export default {
    name: 'addDiseaForm',
    components: {
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            livestockManageId: '',
            isDetail: false,
            isSubmitting: false,
            form: {
                vaccineName: '',        // 疫苗名称
                vaccineDose: '',        // 接种剂量
                operateType: '',        // 接种方式
                company: '',            // 疫苗厂家
                vaccineBatches: '',     // 疫苗批次
                userId: '',             // 牧民Id
                earTagNo: '',           // 耳标编号
                livestockId: '',        // 活畜Id
                remark: '',             // 免疫说明
                operateTime: ''         // 操作日期
            },
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            mindata: "1995-09-09",
            maxdata: '2095-09-09',
            showData: false,
            livestockInfo: null,
            vaccinationList: [],
            rules: {
                earTagNo: [{
                    required: true,
                    message: '请输入耳标号',
                    trigger: ['blur', 'change']
                }],
                operateTime: [{
                    required: true,
                    message: '请选择免疫日期',
                    trigger: ['blur', 'change']
                }],
                vaccineName: [{
                    required: true,
                    message: '请输入疫苗名称',
                    trigger: ['blur', 'change']
                }],
                vaccineDose: [{
                    required: true,
                    message: '请输入接种剂量',
                    trigger: ['blur', 'change']
                }],
                operateType: [{
                    required: true,
                    message: '请选择接种方式',
                    trigger: ['blur', 'change']
                }],
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        },
        pageTitle() {
            return this.isDetail ? '免疫详情' : '新增免疫';
        },
        isFormValid() {
            const { earTagNo, operateTime, vaccineName, vaccineDose, operateType, livestockId } = this.form;
            return earTagNo && operateTime && vaccineName && vaccineDose && operateType && livestockId;
        },
    },

    onLoad(options) {
        this.livestockManageId = options.livestockManageId || '';
        this.isDetail = !!options.livestockManageId;
        this.loadVaccinationDict();

        if (this.livestockManageId) {
            this.getDetail();
        }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules);
        this.$nextTick(() => {
            this.$refs.uForm2?.setRules?.({});
        });
    },

    methods: {
        async loadVaccinationDict() {
            try {
                const res = await getDicts('livestock_vaccination_way');
                if (res && res.data) {
                    this.vaccinationList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载接种方式字典失败:', error);
            }
        },

        selectOperateType(value) {
            this.form.operateType = value;
            this.resetField('operateType');
        },

        // 根据耳标号查询活畜信息
        async searchLivestock() {
            if (!this.form.earTagNo.trim()) {
                this.livestockInfo = null;
                this.form.livestockId = '';
                return;
            }
            try {
                const res = await selectEarTagNo({ earTagNo: this.form.earTagNo });
                if (res.code === 200 && res.result) {
                    this.livestockInfo = res.result;
                    this.form.livestockId = res.result.livestockId;
                    this.form.userId = res.result.userId;
                } else {
                    this.$toast('未找到该耳标号对应的活畜信息');
                    this.livestockInfo = null;
                    this.form.livestockId = '';
                }
            } catch (error) {
                console.error('查询活畜信息失败:', error);
                this.$toast('查询活畜信息失败');
            }
        },

        // 显示日期选择器
        showDatePicker() {
            this.showData = true;
        },

        changeData(e) {
            this.form.operateTime = e.result;
            this.showData = false;
            this.resetField('operateTime');
        },

        // 获取详情
        async getDetail() {
            // TODO: 免疫详情
            console.log('获取免疫详情:', this.livestockManageId);
        },



        async submitForm() {
            if (this.isSubmitting) return;

            if (!this.form.livestockId) {
                this.$toast('请先输入耳标号查询活畜信息');
                return;
            }
            this.isSubmitting = true;
            try {
                const valid = await this.validateForm();
                if (!valid) {
                    return;
                }

                const submitData = {
                    vaccineName: this.form.vaccineName,
                    vaccineDose: this.form.vaccineDose,
                    operateType: this.form.operateType,
                    company: this.form.company,
                    vaccineBatches: this.form.vaccineBatches,
                    userId: this.form.userId || this.userInfo?.userId || '',
                    earTagNo: this.form.earTagNo,
                    livestockId: this.form.livestockId,
                    remark: this.form.remark,
                    operateTime: this.form.operateTime
                };

                const res = await diseaseAdd(submitData);

                if (res.code === 200) {
                    uni.$emit('updateDiseaseList');
                    this.$toast('操作成功');
                    uni.navigateBack({ delta: 1 });
                } else {
                    throw new Error(res.message || '提交失败');
                }
            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        validateForm() {
            return new Promise(resolve => {
                const { earTagNo, operateTime, vaccineName, vaccineDose, operateType, livestockId } = this.form;
                if (!earTagNo) {
                    this.$toast('请输入耳标号');
                    resolve(false);
                    return;
                }
                if (!operateTime) {
                    this.$toast('请选择免疫日期');
                    resolve(false);
                    return;
                }
                if (!vaccineName) {
                    this.$toast('请输入疫苗名称');
                    resolve(false);
                    return;
                }
                if (!vaccineDose) {
                    this.$toast('请输入接种剂量');
                    resolve(false);
                    return;
                }
                if (!operateType) {
                    this.$toast('请选择接种方式');
                    resolve(false);
                    return;
                }
                if (!livestockId) {
                    this.$toast('请先查询活畜信息');
                    resolve(false);
                    return;
                }
                resolve(true);
            });
        },

        handleError(error, customMessage = '') {
            console.error(error);
            this.$toast(error.message || customMessage || '操作失败');
        },

        resetField(prop) {
            let field = this.$refs.uForm?.fields?.find(field => field.prop === prop);
            if (field) {
                field.resetField();
                return;
            }
            field = this.$refs.uForm2?.fields?.find(field => field.prop === prop);
            if (field) {
                field.resetField();
            }
        }
    },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');

.common {
    color: #333;
}

.tips {
    color: #999;
}

.section {
    margin-bottom: 20rpx;

    /deep/ .u-input__textarea {
        padding: 35rpx 0 !important;
    }
}

.base-container {
    padding: 0 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 30rpx 0 0 0;
    margin: 0 30rpx;
}

.section-type {
    padding: 0;
    margin: 0 0 0 15rpx;
    font-size: 14px;
    font-weight: normal !important;
}

.second-section-title {
    padding-top: 0;
}

.operateTime {
    /deep/ .u-form-item--right__content__slot {
        text-align: right !important;
    }
}

.cultivate-section {
    margin: 0;
    background: transparent;

    .cultivate-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;
        margin-top: 10rpx;

        .cultivate-item {
            padding: 16rpx 40rpx;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: #666;
            background-color: #F5F5F5;
            border: 2rpx solid transparent;
            transition: all 0.3s;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                color: #1DB17A;
                background-color: #fff;
                border-color: #1DB17A;
            }

            &.disabled {
                cursor: not-allowed;
                opacity: 0.6;
            }
        }
    }
}

.operateType {
    /deep/ .u-form-item__body {
        display: block !important;
    }
}
</style>
