<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <view v-for="(item, index) in list" :key="index" class="list-item">
          <view class="item-header">
            <text class="item-time">{{ item.operateTime }}</text>
          </view>
          <view class="item-content">
            <view class="content-row">
              <text class="content-label">耳标号：</text>
              <text class="content-value">{{ item.earTagNo }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">活畜状态：</text>
              <text class="content-value">
                <text class="status-tag">
                  {{ getSpiritStateText(item.livestockSpiritState) }}
                </text>
              </text>
            </view>

            <view class="content-row">
              <text class="content-label">采食情况：</text>
              <text class="content-value">
                <text class="status-tag" >
                  {{ getEatStateText(item.livestockEatState) }}
                </text>
              </text>
            </view>
            <view class="content-row" >
              <text class="content-label">异常描述：</text>
              <text class="content-value">{{ item.livestockErrorDesc || '-' }}</text>
            </view>
            <view class="content-row" v-if="item.remark">
              <text class="content-label">备注：</text>
              <text class="content-value remark-text">{{ item.remark }}</text>
            </view>
          </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && list && list.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && list && list.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>

    <view class="fixed-add-btn" @click="addRecord">
      <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
    </view>
  </view>
</template>

<script>
import { dailyPage } from '@/api/pages/livestock/underCare'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'DailyRecord',
  components: {
    nullList
  },
  props: {
    filterParams: {
      type: Object,
      default: () => ({})
    },
    resetSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 10,
      spiritStateDict: {
        1: '正常',
        2: '食欲不振',
        3: '异常活跃',
        4: '患病症状'
      },
      eatStateDict: {
        1: '正常采食',
        2: '采食减少',
        3: '采食增加',
        4: '完全不采食'
      }
    }
  },
  created() {
  },
  mounted() {
    this.getList()

    // 监听日常记录更新事件
    uni.$on('updateDailyList', () => {
      this.pageNum = 1
      this.noMore = false
      this.getList()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateDailyList')
  },
  watch: {
    filterParams: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      },
      deep: true
    },
    resetSearch: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      }
    }
  },
  methods: {
    getList() {
    //   uni.showLoading({ title: '加载中', icon: 'none' })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        earTagNo: this.filterParams.earTagNo || '',
        startTime: this.filterParams.startTime || '',
        endTime: this.filterParams.endTime || ''
      }

      dailyPage(params).then(res => {
        const isSuccess = res.code === 200
        const newList = isSuccess ? (res.result?.list || []) : []
        const total = isSuccess ? parseInt(res.result?.total || 0) : 0

        this.updateList(newList, total)
      }).catch(() => {
        this.updateList([], 0)
      }).finally(() => {
        uni.hideLoading()
      })
    },

    updateList(newList, total) {
      const hasData = total >= 1 && newList.length > 0

      if (this.pageNum >= 2) {
        this.list = [...this.list, ...newList]
        this.noMore = this.list.length >= total
      } else {
        this.isEmpty = !hasData
        this.list = hasData ? [...newList] : []
        this.noMore = hasData ? this.list.length >= total : false
      }

      this.$set(this, 'list', [...this.list])
      this.$forceUpdate()
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    getSpiritStateText(value) {
      return this.spiritStateDict[value] || '未知'
    },

    getEatStateText(value) {
      return this.eatStateDict[value] || '未知'
    },

    getSpiritStateClass(value) {
      if (value === 1) return 'normal'
      if (value === 4) return 'abnormal'
      return 'warning'
    },

    getEatStateClass(value) {
      if (value === 1 || value === 3) return 'normal'
      if (value === 4) return 'abnormal'
      return 'warning'
    },

      addRecord() {
          uni.navigateTo({
              url: '/myPackge5/pages/underCare/addDailyForm'
          });
      }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
