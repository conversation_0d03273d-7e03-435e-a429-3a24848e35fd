<template>
    <view>
        <CustomNavbar :title="'详情'" :titleColor="'##333333'" />
        <view class="header">
            <view class="fifter static-title">养殖场详情</view>
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">

        </view>
        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" />

    </view>
</template>

<script>
import CustomNavbar from '../components/CustomNavbar.vue'
import filterPopup from './components/filterPopup.vue'


export default {
    name: 'inventoryDetails',
    components: {
        CustomNavbar,
        filterPopup
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            filterType: 'inventory',
            pickerFilterShow: false,

        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        }
    },

    onReady() {

    },

    methods: {
        resetSearch() {
            console.log('resetSearch')
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
        },
        fifterClick(){
            this.pickerFilterShow = true;
        },
    }
}
</script>

<style lang="scss" scoped>
.header {
    width: 750rpx;
    height: 250rpx;
    display: flex;
    padding-top: 120rpx;
    box-sizing: border-box;
    position: relative;
    position: relative;
    .static-title {
        left: 30rpx;
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
    }
}

.fifter {
    position: absolute;
    top: 197rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}
</style>
