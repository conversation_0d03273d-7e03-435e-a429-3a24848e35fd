<template>
    <view>
        <CustomNavbar :title="`新建入库`" :titleColor="`#333`" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
             <view class="container">
				<view class="cultivate-section">
				    <view class="section-title">入库类型：</view>
				    <view class="cultivate-options">
				        <view v-for="item in cultivateList" :key="item.value"
				            :class="['cultivate-item', { 'active': form.inventoryType === item.value }]"
				            @click="!isDetail && selectCultivateType(item.value)">
				            {{ item.label }}
				        </view>
				    </view>
				</view>
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
					
					<template v-if="form.inventoryType == 1">
					   <u-form-item label="订单编号：" required 
					       :right-icon="isDetail ? '' : 'arrow-right'">
					       <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
					           :value="`11`" placeholder="请选择订单编号" disabled
					           @click="!isDetail && (showOrder = true)" />
					   </u-form-item>
					   <u-form-item label="供应商" required >
					       <u-input v-model="form.pastureName" maxlength="10" placeholder="请输入供应商"
					           :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
					   </u-form-item>
					   <u-form-item label="批次号" required >
					       <u-input v-model="form.pastureName" maxlength="10" placeholder="请输入批次号"
					           :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
					   </u-form-item>
					</template>
					
					<view class="container" style="padding: 0px; margin: 25rpx 0;">
						<view class="cultivate-section">
						    <view class="section-title">入库模式：</view>
						    <view class="cultivate-options">
						        <view v-for="item in rukuMode" :key="item.value"
						            :class="['cultivate-item', { 'active': form.earTagType === item.value }]"
						            @click="form.earTagType = item.value">
						            {{ item.label }}
						        </view>
						    </view>
						</view>
					</view>
					<template v-if="form.earTagType == '0'">
						<u-form-item label="活畜类别" required prop="pastureName" :right-icon="'arrow-right'">
							<u-input v-model="liveType" placeholder="请选择活畜类别"
									:custom-style="customStyle" @click="typeShow = true" :placeholder-style="placeholderStyle" :disabled="true" />
						</u-form-item>
						
						<u-form-item label="活畜数量（头）" required prop="num">
							<u-input v-model="form.recordList[0].num" maxlength="10" placeholder="请选择（不能超过订单数量）"
								:custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
						</u-form-item>
					</template>
                    <u-form-item label="疫苗接种情况" required prop="pastureNature"
                        :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getNatureText(form.pastureNature)" placeholder="请选择疫苗接种情况" disabled
                            @click="!isDetail && (showNatureSelect = true)" />
                    </u-form-item>
                    <u-form-item label="疾病史"  prop="diseaseHistory" >
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="form.diseaseHistory"
                            placeholder="请输入疾病史"  />
                    </u-form-item>
                    <u-form-item label="检疫结果" prop="quarantineResult" v-if="getNatureText(form.pastureNature) =='已接种'">
                        <u-input v-model="form.quarantineResult" placeholder="请输入检疫结果" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" :disabled="isDetail" />
                    </u-form-item>
					
					<u-form-item label="入库日期" required prop="stockInDate"  :right-icon="'arrow-right'">
					    <u-input v-model="form.stockInDate" placeholder="请选择入库日期" :custom-style="customStyle"
					        :placeholder-style="placeholderStyle" :disabled="true"  @click="showData = true" />
					</u-form-item>

                    <!-- 养殖场 -->
                    <u-form-item label="养殖场" required prop="pastureId" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input v-model="form.pastureName" placeholder="请选择请选择" type="number"
                            :custom-style="customStyle"  @click="(pastureSelect = true)" :placeholder-style="placeholderStyle" :disabled="true" />
                    </u-form-item>
					<u-form-item label="圈舍" required prop="penName" :right-icon="isDetail ? '' : 'arrow-right'">
					    <u-input v-model="form.penName" placeholder="请选择圈舍" type="number"
					        :custom-style="customStyle"  @click="(penSelect = true)" :placeholder-style="placeholderStyle" :disabled="true" />
					</u-form-item>
                </u-form>
                
            </view>
            <!-- <view class="upload-section">
                <view class="section-title">
                    养殖场照片：
                    <img v-if="!isDetail" class="upload-icon" src="../../icon/photo.png"
                        @click="uploadImage('farmImages', 3)"></img>
                </view>
                <view class="section-subtitle">最多不超过3张，单张图片小于2M</view>

                <view class="uploadImage" v-if="form.farmImages.length">
                    <view class="itemAlready" v-for="(item, index) in form.farmImages" :key="index">
                        <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
                        <view v-if="!isDetail" class="closeIcon" @click="deleteImage(index, 'farmImages')"></view>
                    </view>
                </view>
            </view> -->
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">确认</view>
        </view>

        <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow"
            @addressCanel="pickerAreaShow = false" :titleShow="false" :showLevel3="true" />

        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showNatureSelect" mode="single-column"
            :list="yimaioMode" label-name="label" @confirm="selectNature" value-name="value" />
			
		
		<u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="pastureSelect" mode="single-column"
		    :list="pastureList" label-name="pastureName" @confirm="selectPasture" value-name="pastureId" />
			
		<u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="penSelect" mode="single-column"
		    :list="penList" label-name="penName" @confirm="selectPen" value-name="penId" />
			
		<u-select v-model="typeShow" confirm-color='#40CA8F' mode="mutil-column" :list="typeList" @confirm="confirmType"></u-select>
			
	    <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`date`" @change='changeData'  :max-date='maxdata' :min-date="mindata"></u-calendar>
	    
		<u-popup v-model="showOrder" mode='bottom' height ='85%' :closeable="true">
		   <orderList></orderList>
		</u-popup>
		
    </view>
</template>

<script>
import addressPicker from '@/components/address-picker/index.vue'
import { pastureAdd, pastureEdit, pastureDetail } from '@/api/pages/livestock/farm'
import {
    getDicts,
    livestockCategory,
    animalTypeList
} from "@/api/dict.js"

import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../components/CustomNavbar.vue'
import { pasturePage,penList } from '@/api/pages/livestock/farm'
import {
	 myTradingList
} from '@/api/account.js'
import orderList from '../components/orderList.vue'
import { purchaseOrderPage } from '@/api/pages/purchaseOrder'
export default {
    name: 'addFarm',
    components: {
        addressPicker,
        CustomNavbar,
		orderList
    },

    data() {
        return {
			mindata: "2024-01-01",
			maxdata:'2095-09-09',
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            // 页面状态
            pastureId: '', // 养殖场ID
			pastureList:[],
			penList:[],
			typeList:[],
			showOrder:false,
            //isEdit: false, // 是否编辑模式
            isDetail: false, // 是否详情模式
			showData:false,
			typeShow:false,
            form: {
				activeType:1,
                pastureName: '',
				penName:'',
				earTagType:'0',
                quarantineResult: '',
                pastureNature: '',
                inventoryType: '1',
                diseaseHistory: '',              
				stockInDate:'',
				recordList:[
					{
						num:'',
						typeId:"403292860613267456",
						typeName:"牛",
						varietiesId:"",
						varietiesName:"",
						categoryId:"",
						categoryName:""
					}
				]
            },
            pickerAreaShow: false,
            showNatureSelect: false,
			pastureSelect:false,
			penSelect:false,
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            isSubmitting: false,
            natureList: [],
            cultivateList: [{
				value:"1",
				label:"外购"
			},{
				value:"2",
				label:"自繁"
			},{
				value:"3",
				label:"调拨"
			}],
			//rukuModeVal: "1",
			rukuMode:[
				{
					value:"0",
					label:"无耳标入库"
				},{
					value:"1",
					label:"耳标入库"
				}
			],
			yimaioMode:[
				{
					value:"1",
					label:"已接种"
				},{
					value:"2",
					label:"未接种"
				}
			]
           
        }
    },

    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        },
        
        isFormValid() {
            const { pastureName, provinceName, address, pastureNature, cultivateType, farmImages } = this.form
            return pastureName && provinceName && address && pastureNature !== '' && cultivateType !== '' && farmImages.length > 0
        },
		liveType(){
			if(!(this.form.recordList[0].varietiesName || this.form.recordList[0].varietiesName)) return "";
			return `${this.form.recordList[0].varietiesName } - ${this.form.recordList[0].categoryName}` 
		},
    },

    onLoad(options) {
        // 判断页面模式
        if (options.pastureId) {
            this.pastureId = options.pastureId;
            //this.isEdit = options.mode === 'edit';
            this.isDetail = options.mode === 'detail';
        }
        //初始化数据
        this.initData();
       // this.loadNatureDict();
       // this.loadCultivateDict();

        // 编辑或详情
       /* if (this.pastureId) {
            this.loadFarmDetail();
        } */
    },

   /* onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    }, */

    methods: {
		/**
		 * 初始化数据
		 */
		async initData() {			 
		     const pasture = await pasturePage({ pageNum: 1, pageSize: 1000 });		   
			 if(pasture.code == 200){
				 this.pastureList = pasture.result.list;
				 console.log(this.pastureList)
			 }
			 livestockCategory({
				pageNum: 1,
				pageSize: 100000,
				categoryType: '403292860613267456'
			}).then(res => {				
				this.typeList[0] = res.result.map(item =>{
					 return {
						 value : item.varietiesId,
						 label : item.varietiesName
					 }
				}) || [];
			})
			animalTypeList({
				pageSize: 9999,
				pageNum: 1,
				categoryType: '403292860613267456'
			}).then(res => {
				this.typeList[1] = res.result.map(item =>{
					 return {
						 value : item.categoryId,
						 label : item.categoryName
					 }
				}) || [];
			});
			
			
			purchaseOrderPage({
				pageNum: 1,
				pageSize: 1000,
				//showStatus: 42 //已验收
			}).then(r => {
				console.log(r);
			})
			 
		},
		changeData(e){
			this.form.stockInDate = e.result;
			this.showData = false;
		},
        /* async loadNatureDict() {
            try {
                const res = await getDicts('pasture_nature');
                if (res && res.data) {
                    this.natureList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载字典失败:', error);
                this.$toast('加载字典失败');
            }
        }, */

       /* async loadCultivateDict() {
            try {
                const res = await getDicts('cultivate_type');
                if (res && res.data) {
                    this.cultivateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载养殖方式字典失败:', error);
            }
        }, */

        // 加载养殖场详情
       /* async loadFarmDetail() {
            try {
                const res = await pastureDetail({ pastureId: this.pastureId });
                if (res.code === 200 && res.result) {
                    const data = res.result;
                    this.form = {
                        pastureName: data.pastureName || '',
                        address: data.address || '',
                        breedingQuantity: data.breedingQuantity || '',
                        pastureNature: data.pastureNature || '',
                        cultivateType: data.cultivateType || '',
                        urls: data.farmImages ? data.farmImages.split(',').filter(img => img) : [],
                        provinceName: data.provinceName || '',
                        provinceId: data.provinceId || '',
                        cityName: data.cityName || '',
                        cityId: data.cityId || '',
                        countyName: data.countyName || '',
                        countyId: data.countyId || ''
                    };
                } else {
                    throw new Error(res.message || '获取详情失败');
                }
            } catch (error) {
                console.error('加载养殖场详情失败:', error);
                this.$toast('加载详情失败');
                uni.navigateBack();
            }
        }, */

        getNatureText(value) {
            const nature = this.yimaioMode.find(item => item.value === value)
            return nature ? nature.label : ''
        },

        submitAddress(val) {
            if (!val?.areaName || !val?.areaValue) return

            const areaName = val.areaName.split('-')
            const areaId = val.areaValue.split(',')

            this.form = {
                ...this.form,
                provinceName: areaName[0] || '',
                provinceId: areaId[0] || '',
                cityName: areaName[1] || '',
                cityId: areaId[1] || '',
                countyName: areaName[2] || '',
                countyId: areaId[2] || ''
            }

            this.pickerAreaShow = false
            this.resetField('provinceName')
        },

        // 选择疫苗接种情况
        selectNature(value) {
            if (!value?.length) return
            this.form.pastureNature = value[0].value
            this.showNatureSelect = false
            this.resetField('pastureNature')
        },
		/**
		 * 选择养殖场
		 */
		selectPasture(value){
			
			if (!value?.length) return
			const item = value[0];
			const pastureItem = this.pastureList.find(i => i.pastureId == item.value);
			console.log(pastureItem)
			this.form.pastureName = pastureItem.pastureName; 
			this.getPenList(pastureItem.pastureId);
			this.pastureSelect = false;
			this.resetField('pastureName')
		},
		
		async getPenList(pastureId){
			 const that = this;
			 console.log(pastureId)
			 const res = await penList({pastureId});
			  if(res.code == 200){
				  that.penList = res.result;
			  }
		},
		selectPen(value){
			if (!value?.length) return
			const item = value[0];
			const penItem = this.penList.find(i => i.penId == item.value);
			this.form.penName = penItem.penName; 
			this.penSelect = false;
			this.resetField('penName')
		},
		confirmType(value){			
			this.form.recordList[0].varietiesId = value[0].value;
			this.form.recordList[0].varietiesName = value[0].label;
			this.form.recordList[0].categoryId = value[1].value;
			this.form.recordList[0].categoryName = value[1].label;
		},
        /**
		 * 选择入栏类型
		 * @param {Object} value 
		 */
        selectCultivateType(value) {
            this.form.inventoryType = value
            this.resetField('inventoryType')
        },

        // 上传图片
       /* uploadImage(type, maxCount) {
            if (this.form[type].length >= maxCount) {
                this.$toast(`图片最多只能上传${maxCount}张`)
                return
            }
            const that = this
            uni.chooseImage({
                count: maxCount - this.form[type].length,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: function (res) {
                    res.tempFilePaths.forEach(filePath => {
                        uploadFiles({
                            filePath: filePath,
                        }).then((data) => {
                            that.form[type].push(data)
                            that.resetField(type)
                        }).catch(error => {
                            console.error('上传失败:', error)
                            that.$toast('上传失败')
                        })
                    })
                },
                fail(e) {
                    console.error('选择图片失败:', e)
                },
            })
        },

        // 预览图片
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }, 

        // 删除图片
        deleteImage(index, type) {
            this.form[type].splice(index, 1)
            this.resetField(type)
            this.$forceUpdate()
        },*/

        // 提交表单
        async submitForm() {
            if (this.isSubmitting) return
            // if (!this.isFormValid) {
            //     return this.$toast('请填写完整信息')
            // }

            try {
                // this.isSubmitting = true
                // const valid = await this.validateForm()
                // if (!valid) return

                // 准备提交数据
                // const submitData = {
                //     ...this.form,
                //     urls: this.form.farmImages
                // };

                // 如果是编辑模式，添加ID
                // if (this.isEdit) {
                //     submitData.pastureId = this.pastureId;
                // }

                // const res = this.isEdit
                //     ? await pastureEdit(submitData)
                //     : await pastureAdd(submitData);

                // if (res.code === 200) {
                //     uni.$emit('updateFarmList')
                //     this.$toast(this.isEdit ? '保存成功' : '添加成功')
                //     uni.navigateBack({ delta: 1 })
                // } else {
                //     throw new Error(res.message || '提交失败')
                // }
            } catch (error) {
                this.handleError(error, '提交失败')
            } finally {
                this.isSubmitting = false
            }
        },

        // validateForm() {
        //     return new Promise(resolve => {
        //         this.$refs.uForm.validate(valid => resolve(valid))
        //     })
        // },

        handleError(error, customMessage = '') {
            console.error(error)
            this.$toast(error.message || customMessage || '操作失败')
        },

        resetField(value) {
            if (!value) return
            this.$refs.uForm?.fields?.forEach(field => {
                if (field.prop === value) {
                    field.resetField()
                }
            })
        }
    },
}
</script>

<style lang="less" scoped>
	@import url('../../css/index.less');
</style>
