/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
@import 'uview-ui/theme.scss';
@import "common/css/flex.scss";
@import "common/css/theme.scss";
@import "common/css/common.scss";


canvas {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}
.box{display: flex; align-items: start;}
	 .flex{
		 flex: 1;
	 }
.bold{
	font-weight: bold;
}