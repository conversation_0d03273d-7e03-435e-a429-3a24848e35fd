{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
			"^g-(.*)": "@/components/g-$1/g-$1.vue"
		}
	},
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "牛贸帮",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/mine/index",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/account/login/login",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/account/retrievePwd",
			"style": {
				"navigationBarTitleText": "修改密码"
			}
		},
		{
			"path": "pages/commitment/index",
			"style": {
				// "navigationBarTitleText": "承诺书",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}

		}
		
	],
	"subpackages": [
		// 销售合同
		{
			"root": "myPackge1",
			"pages":[
				{
					"path": "pages/salesContract/index",
					"style": {
						"navigationBarTitleText": "销售合同",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/salesContract/createContract",
					"style": {
						"navigationBarTitleText": "销售合同"
					}
				},
				{
					"path": "pages/salesContract/contractDetail",
					"style": {
						"navigationBarTitleText": "合同详情"
					}
				},
				{
					"path": "pages/weeklyOrders/index",
					"style": {
						"navigationBarTitleText": "周订单",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/weeklyOrders/form",
					"style": {
						"navigationBarTitleText": "新增周订单"
					}
				},
				{
					"path": "pages/weeklyOrders/detail",
					"style": {
						"navigationBarTitleText": "订单详情"
					}
				},
				{
					"path": "pages/purchase/index",
					"style": {
						"navigationBarTitleText": "采购计划",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/purchase/form",
					"style": {
						"navigationBarTitleText": "新增采购计划"
					}
				},
				{
					"path": "pages/purchase/detail",
					"style": {
						"navigationBarTitleText": "订单详情"
					}
				},
				{
					"path": "pages/payment/index",
					"style": {
						"navigationBarTitleText": "付款详情"
					}
				},
				{
					"path": "pages/payment/pay",
					"style": {
						"navigationBarTitleText": "付款"
					}
				},
				{
					"path": "pages/payment/detail",
					"style": {
						"navigationBarTitleText": "付款"
					}
				},
				{
					"path": "pages/payment/form",
					"style": {
						"navigationBarTitleText": "费用详情"
					}
				},
				{
					"path": "pages/sale/index",
					"style": {
						"navigationBarTitleText": "销售订单",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/sale/form",
					"style": {
						"navigationBarTitleText": "调整订单"
					}
				},
				{
					"path": "pages/sale/detail",
					"style": {
						"navigationBarTitleText": "销售订单详情"
					}
				},
				{
					"path": "pages/settlement/index",
					"style": {
						"navigationBarTitleText": "周订单结算"
					}
				},
				{
					"path": "pages/settlement/detail",
					"style": {
						"navigationBarTitleText": "结算详情"
					}
				},
				{
					"path": "pages/statistical/index",
					"style": {
						"navigationBarTitleText": "统计分析",
						"navigationStyle": "custom"
					},
					"enablePullDownRefresh": true,
					"usingComponents": {
					    "qiun-wx-ucharts": "/components/qiun-wx-ucharts/index"
					  }
				},
				{
					"path": "pages/statistical/analysis/sale",
					"style": {
						"navigationBarTitleText": "销售分析"
					}
				},
				{
					"path": "pages/statistical/analysis/profit",
					"style": {
						"navigationBarTitleText": "利润分析"
					}
				},
				{
					"path": "pages/statistical/analysis/price",
					"style": {
						"navigationBarTitleText": "价格分析"
					}
				},
				{
					"path": "pages/statistical/analysis/cost",
					"style": {
						"navigationBarTitleText": "成本分析"
					}
				},
				{
					"path": "pages/statistical/analysis/calf",
					"style": {
						"navigationBarTitleText": "牛源分析"
					}
				},
				{
					"path": "pages/statistical/analysis/transport",
					"style": {
						"navigationBarTitleText": "运输分析"
					}
				},
				{
					"path": "pages/statistical/analysis/details/saleDetail",
					"style": {
						"navigationBarTitleText": "销售详情"
					}
				},
				{
					"path": "pages/statistical/analysis/details/profitDetail",
					"style": {
						"navigationBarTitleText": "利润详情"
					}
				},
				{
					"path": "pages/statistical/analysis/details/priceDetail",
					"style": {
						"navigationBarTitleText": "价格详情"
					}
				},
				{
					"path": "pages/statistical/analysis/details/costDetail",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "pages/statistical/analysis/details/calfDetail",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "pages/statistical/analysis/details/transportDetail",
					"style": {
						"navigationBarTitleText": "运输详情"
					}
				},
				{
					"path": "pages/arrive/index",
					"style": {
						"navigationBarTitleText": "抵达"
					}
				},
				{
					"path": "pages/arrive/info",
					"style": {
						"navigationBarTitleText": "抵达详情"
					}
				},
				{
					"path": "pages/checkAccept/cowSource",
					"style": {
						"navigationBarTitleText": "牛源验收"
					}
				},
				{
					"path": "pages/checkAccept/transport",
					"style": {
						"navigationBarTitleText": "运输验收"
					}
				},
				{
					"path": "pages/checkAccept/quality",
					"style": {
						"navigationBarTitleText": "品质验收"
					}
				},
				{
					"path": "pages/checkAccept/invoice",
					"style": {
						"navigationBarTitleText": "发票验收"
					}
				},
				{
					"path": "pages/checkAccept/result",
					"style": {
						"navigationBarTitleText": "验收结果"
					}
				},
				{
					"path": "pages/checkAccept/detail",
					"style": {
						"navigationBarTitleText": "验收详情"
					}
				},
				{
					"path": "pages/checkAccept/handwriting",
					"style": {
						"navigationBarTitleText": "签字"
					}
				},
				{
					"path": "pages/transport/index",
					"style": {
						"navigationBarTitleText": "运输保养",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/transport/form",
					"style": {
						"navigationBarTitleText": "运输保养"
					}
				},
				{
					"path": "pages/transport/detailList",
					"style": {
						"navigationBarTitleText": "保养记录"
					}
				},
				{
					"path": "pages/transport/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "pages/finance/index",
					"style": {
						"navigationBarTitleText": "财务管理",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/finance/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				}
			]
		},
		{
			"root": "myPackge2",
			"pages":[
				{
					"path": "pages/catchingCows/index",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "pages/catchingCows/checkCows/index",
					"style": {
						"navigationBarTitleText": "甄选"
					}
				},
				{
					"path": "pages/catchingCows/checkCows/createCows",
					"style": {
						"navigationBarTitleText": "甄选"
					}
				},
				{
					"path": "pages/catchingCows/controlSlot",
					"style": {
						"navigationBarTitleText": "控槽"
					}
				},
				{
					"path": "pages/catchingCows/quarantine",
					"style": {
						"navigationBarTitleText": "检疫"
					}
				},
				{
					"path": "pages/catchingCows/catchCows",
					"style": {
						"navigationBarTitleText": "收购"
					}
				},
				{
					"path": "pages/catchingCows/earTagNumber",
					"style": {
						"navigationBarTitleText": "耳标编号"
					}
				},
				{
					"path": "pages/catchingCows/weighing",
					"style": {
						"navigationBarTitleText": "检查"
					}
				},
				{
					"path": "pages/catchingCows/departure",
					"style": {
						"navigationBarTitleText": "整车"
					}
				},
				{
					"path": "pages/catchingCows/cowDetails",
					"style": {
						"navigationBarTitleText": "选牛详情"
					}
				},
				{
					"path": "pages/catchingCows/details/controlSlotDetail",
					"style": {
						"navigationBarTitleText": "控槽详情"
					}
				},
				{
					"path": "pages/catchingCows/details/quarantineDetail",
					"style": {
						"navigationBarTitleText": "检疫详情"
					}
				},
				{
					"path": "pages/catchingCows/details/catchCowsDetail",
					"style": {
						"navigationBarTitleText": "收购详情"
					}
				},
				{
					"path": "pages/catchingCows/details/weighingDetail",
					"style": {
						"navigationBarTitleText": "检查详情"
					}
				},
				{
					"path": "pages/catchingCows/details/departureDetail",
					"style": {
						"navigationBarTitleText": "发车详情"
					}
				}
			]
		},
		{
			"root": "myPackge3",
			"pages":[
				{
					"path": "pages/handwriting",
					"style": {
						"navigationBarTitleText": "签字"
					}
				}
			]
		},
		{
			"root": "myPackge4",
			"pages":[
				{
					"path": "pages/protocol",
					"style": {
						"navigationBarTitleText": "协议"
					}
				}
			]
		},
		// 活畜管理
		{
			"root": "myPackge5",
			"pages":[
				{
					"path": "pages/underCare/index",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/index",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/farm/index",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/farm/addFarm",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/ruku/index",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/ruku/addForm",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/underCare/addFeedForm",
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/underCare/addGrowForm",
					"style":{
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/underCare/addDiseaForm",
					"style":{
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/underCare/addDailyForm",
					"style":{
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pages/underCare/inventoryDetails",
					"style":{
						"navigationStyle": "custom"
					}
				}

			]
		}
	],
	"globalStyle": {
		"navigationBarBackgroundColor": "#fff",
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": ""
	},
	"condition": {
		"current": 0,
		"list": [{
			"name": "",
			"path": "/pages/index/index",
			"query": ""
		}]
	},
	"tabBar": {
		"color": "#333333", // 默认字体颜色
		"selectedColor": "#16B251", // 选中字体颜色
		"borderStyle": "black", // tabBar 上边框的颜色，仅支持 black / white
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "/static/img/hmicon.png",
				"selectedIconPath": "/static/img/hmicon_on.png"
			},
			{
				"pagePath": "pages/mine/index",
				"text": "我的",
				"iconPath": "/static/img/user.png",
				"selectedIconPath": "/static/img/user_on.png"
			}
		]
	}
}