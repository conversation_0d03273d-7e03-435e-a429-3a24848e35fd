// 托管列表共同样式
.under-care-container {
  position: relative;
  height: calc(100vh - 400rpx);
  display: flex;
  flex-direction: column;
  // background-color: #f5f5f5;
}

.under-care-list {
  flex: 1;
  padding: 0;
  box-sizing: border-box;
}

.list-content {
  padding-bottom: 40rpx;
}

.list-item {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.item-header {
  height: 56rpx;
  background: linear-gradient(260deg, #60D26F 0%, #1CC271 100%);
  line-height: 56rpx;
  padding: 0 26rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-time {
  flex: 1;
}
.oper-detail{
  text-align: right;
  font-size: 24rpx;
  margin-right: 10rpx;
}

.item-content {
  padding: 25rpx;
  background: #FFFFFF;
}

.content-row {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  line-height: 38rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.content-label {
  color: #999999;
  font-weight: 400;
}

.content-value {
  color: #333333;
  font-weight: 400;
  flex: 1;
}

.fixed-add-btn {
  position: fixed;
  right: 10rpx;
  bottom: 290rpx;
  width: 130rpx;
  height: 122rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  &:active {
    transform: scale(0.95);
    transition: transform 0.1s;
  }
  img{
    width: 100%;
    height: 100%;
  }
}

.add-icon {
  width: 40rpx;
  height: 40rpx;
}
// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999999;
  font-size: 28rpx;
}

// 加载更多
.load-more {
  text-align: center;
  padding: 30rpx;
  color: #999999;
  font-size: 24rpx;
}

// 字典映射样式
.dict-value {
  color: #1CC271;
  font-weight: 500;
}

// 状态标签
.status-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  
  &.normal {
    background: #E8F5E8;
    color: #1CC271;
  }
  
  &.abnormal {
    background: #FFF2F0;
    color: #FF4D4F;
  }
  
  &.warning {
    background: #FFF7E6;
    color: #FA8C16;
  }
}


